# Fintech Invoice iOS App

A native iOS application for the Israeli B2B invoicing system, built with SwiftUI and designed to match the existing web application's black/white minimalist aesthetic.

## Features

- **Native iOS Design**: Matches the existing web app design system with black/white minimalist aesthetic
- **RTL Support**: Full Hebrew language support with right-to-left layout
- **Multi-step Registration**: Same survey flow as the web application
- **Authentication**: Secure login/logout with Supabase integration
- **Dashboard**: Overview of invoices, expenses, and business metrics
- **Dark Theme**: Consistent with the cosmic design system

## Requirements

- iOS 15.0+
- Xcode 15.0+
- Swift 5.9+

## Setup Instructions

### 1. Configure Supabase Credentials

1. Open `FintechInvoice/Config.plist`
2. Replace the placeholder values with your actual Supabase credentials:
   ```xml
   <key>SUPABASE_URL</key>
   <string>https://your-project.supabase.co</string>
   <key>SUPABASE_ANON_KEY</key>
   <string>your-anon-key-here</string>
   ```

### 2. Open in Xcode

1. Navigate to the `apps/mobile` directory
2. Double-click `FintechInvoice.xcodeproj` to open in Xcode
3. Select your development team in the project settings
4. Choose a simulator or connected device
5. Press Cmd+R to build and run

### 3. Install Dependencies

The project uses Swift Package Manager for dependencies. Xcode will automatically resolve and install:

- Supabase Swift SDK
- Auth, PostgREST, Storage, and Realtime modules

## Project Structure

```
FintechInvoice/
├── FintechInvoiceApp.swift          # Main app entry point
├── ContentView.swift                # Root view with authentication logic
├── Models/                          # Data models
├── Views/
│   ├── Auth/                        # Authentication screens
│   │   ├── AuthenticationView.swift # Login/Register container
│   │   ├── RegisterView.swift       # Multi-step registration
│   │   └── RegistrationSteps.swift  # Individual survey steps
│   ├── Dashboard/                   # Main app screens
│   │   ├── MainTabView.swift        # Tab navigation
│   │   └── PlaceholderViews.swift   # Other tab content
│   └── Components/                  # Reusable UI components
├── ViewModels/                      # MVVM view models
│   ├── AuthViewModel.swift          # Authentication logic
│   └── DashboardViewModel.swift     # Dashboard data
├── Services/                        # API and business logic
│   └── SupabaseService.swift        # Supabase integration
├── Utils/                          # Utilities and extensions
│   └── DesignSystem.swift          # Design tokens and styling
├── Assets.xcassets/                # App icons and colors
├── Info.plist                     # App configuration
└── Config.plist                    # Supabase credentials
```

## Design System

The app follows the same design system as the web application:

### Colors
- **Background**: Dark charcoal (#141414)
- **Foreground**: Light grey (#F2F2F2)
- **Primary**: Light grey (#D9D9D9)
- **Card**: Slightly lighter charcoal (#1F1F1F)
- **Accent**: Medium grey (#B3B3B3)

### Typography
- **Hebrew**: SecularOne font family (with fallbacks)
- **English**: Inter font family (with fallbacks)
- **RTL Support**: All text inputs and layouts support right-to-left

### Components
- **Cosmic Cards**: Glass-morphism effect with subtle borders
- **Cosmic Glow**: Hover effects with subtle shadows
- **Progress Indicators**: Custom styled progress bars
- **Buttons**: Primary, secondary, and survey button styles

## Authentication Flow

The app implements the same multi-step registration as the web application:

1. **Industry & Revenue**: Business type and annual revenue selection
2. **Business Details**: Company information (Hebrew name, address, etc.)
3. **Account Details**: User credentials and personal information
4. **Survey Questions**: Three separate screens for:
   - Accounting services interest
   - Business insurance interest
   - Business loans interest

## App Store Compliance

The project is configured for App Store submission:

- **Privacy**: Camera and photo library usage descriptions
- **Localization**: Hebrew and English support
- **Security**: HTTPS-only network requests
- **Orientation**: Portrait-only for iPhone
- **Deployment Target**: iOS 15.0+

## Development Notes

### Adding New Features

1. Follow the MVVM architecture pattern
2. Use the design system tokens from `DesignSystem.swift`
3. Ensure RTL support for all new UI components
4. Add Hebrew translations for all user-facing text

### Testing

- Use Xcode's built-in simulator for testing
- Test on both iPhone and iPad (if supporting iPad in the future)
- Verify RTL layout on Hebrew language settings
- Test authentication flow with actual Supabase backend

### Deployment

1. Configure proper development team and certificates
2. Update version numbers in project settings
3. Generate app icons for all required sizes
4. Test on physical devices before submission
5. Follow Apple's App Store Review Guidelines

## Troubleshooting

### Common Issues

1. **Supabase Connection Failed**
   - Verify credentials in `Config.plist`
   - Check network connectivity
   - Ensure Supabase project is active

2. **Build Errors**
   - Clean build folder (Cmd+Shift+K)
   - Reset package caches in Xcode
   - Verify iOS deployment target

3. **RTL Layout Issues**
   - Check `environment(\.layoutDirection, .rightToLeft)`
   - Verify text alignment settings
   - Test with Hebrew system language

## Contributing

When contributing to the mobile app:

1. Maintain consistency with the web app design
2. Follow Swift coding conventions
3. Add appropriate comments for complex logic
4. Test on multiple device sizes
5. Ensure accessibility compliance

## License

This project is part of the Fintech Invoice application suite.
