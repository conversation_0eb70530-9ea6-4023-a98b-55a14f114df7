# XcodeGen project configuration for Fintech Invoice App
name: FintechInvoice
options:
  bundleIdPrefix: com.fintech.invoice
  deploymentTarget:
    iOS: "15.0"
  developmentLanguage: en
  indentWidth: 2
  tabWidth: 2
  usesTabs: false

settings:
  base:
    DEVELOPMENT_TEAM: ""
    MARKETING_VERSION: "1.0.0"
    CURRENT_PROJECT_VERSION: "1"
    IPHONEOS_DEPLOYMENT_TARGET: "15.0"
    SWIFT_VERSION: "5.0"
    ENABLE_BITCODE: false
    SUPPORTS_MACCATALYST: false

targets:
  FintechInvoice:
    type: application
    platform: iOS
    deploymentTarget: "15.0"
    sources:
      - path: FintechInvoice
        excludes:
          - "*.md"
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.fintech.invoice.app
        INFOPLIST_FILE: FintechInvoice/Info.plist
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor
        DEVELOPMENT_ASSET_PATHS: FintechInvoice/Preview\ Content
        ENABLE_PREVIEWS: true
        SWIFT_EMIT_LOC_STRINGS: true
        SUPPORTS_MACCATALYST: false
        TARGETED_DEVICE_FAMILY: "1"
        ONLY_ACTIVE_ARCH: true
    dependencies:
      - package: Supabase
        product: Supabase
      - package: Supabase
        product: Auth
      - package: Supabase
        product: PostgREST
      - package: Supabase
        product: Storage
      - package: Supabase
        product: Realtime

packages:
  Supabase:
    url: https://github.com/supabase/supabase-swift
    from: "2.5.1"

schemes:
  FintechInvoice:
    build:
      targets:
        FintechInvoice: all
    run:
      config: Debug
    test:
      config: Debug
    profile:
      config: Release
    analyze:
      config: Debug
    archive:
      config: Release
