// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0319D8B5963CD77A4B657B93 /* SupabaseService.swift in Sources */ = {isa = PBXBuildFile; fileRef = C1343C8EBD74D328485A2BD6 /* SupabaseService.swift */; };
		0EDEE960F296DACBFF57FC4A /* RegisterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1773BEC191855B64E3F5B28A /* RegisterView.swift */; };
		0F9CF5713392CB926249549D /* CustomerSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A7BD3F3A5AC7143828CD73E /* CustomerSelectionView.swift */; };
		110528DC201466540BB2F43B /* ErrorTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2915C2F96130682B73B68EF7 /* ErrorTypes.swift */; };
		1807F7C98ECDFB17D71DED34 /* PlaceholderViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 78669ED8016BF4CFE7FD95EA /* PlaceholderViews.swift */; };
		21D36FCBC62D153DD6A2722E /* EmailVerificationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE7A3E6A6DC96442CE0F98EC /* EmailVerificationView.swift */; };
		24F6891F11136EADE43B2B0C /* Storage in Frameworks */ = {isa = PBXBuildFile; productRef = 005C84C91F61AF4C98003B67 /* Storage */; };
		2544D33545D136DE9E3A48B3 /* Realtime in Frameworks */ = {isa = PBXBuildFile; productRef = B5406BB05832354DE1B60497 /* Realtime */; };
		284DA0D6ABC0E90455ADD27C /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3BCE9960FDBC5BB2FB74A9BB /* ContentView.swift */; };
		29A2E909743D24A504E7F82F /* CreateCustomerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F550CD2E813D22636D5F3DBE /* CreateCustomerView.swift */; };
		36CB2BE71B2F3D2B0062DA96 /* DesignSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = EA1106ED524801AD60B32D5B /* DesignSystem.swift */; };
		46EDED4198FE610241DF5F42 /* Config.plist in Resources */ = {isa = PBXBuildFile; fileRef = ABB108C6F494B6468D4FAF64 /* Config.plist */; };
		494F932B33D77944969B92F6 /* AuthViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2DFFBF1771BBF1758DDDEA5 /* AuthViewModel.swift */; };
		4D41A3DBCD63B66B220687AA /* DocumentCreationWizardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 32AA44B8C53D3CFF4E355B6B /* DocumentCreationWizardView.swift */; };
		5115DDA2EDC2F092BB9CEB9C /* DocumentItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 538C2A4EAC19941B4D1E2E8A /* DocumentItem.swift */; };
		5F44A5BBCF494591736DC396 /* Product.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA4DAF1FEB36486AA082CE9 /* Product.swift */; };
		6254EF8EDF32EE4844A7B55A /* DocumentSummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5E74125F0E956FE8C1A2A65E /* DocumentSummaryView.swift */; };
		62761EC8E399A0BBFE635EC2 /* FintechInvoiceApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4F74AA9D812579A1A9325B51 /* FintechInvoiceApp.swift */; };
		6A2BBBAA14F2C841BD66D042 /* AuthenticationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6458A0CF4A679EF1B298D1B /* AuthenticationView.swift */; };
		7CE3644E450CD59E6E3D53A5 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 425DCEA6B47516937194DF0D /* Assets.xcassets */; };
		82477D2D4E74068EB7EB1E55 /* RegistrationSteps.swift in Sources */ = {isa = PBXBuildFile; fileRef = 771770B367E8B88D4C6116A0 /* RegistrationSteps.swift */; };
		82F57E5B82F7C5A46347A7E5 /* MailComposerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 465894914B61D5E4B15E8412 /* MailComposerView.swift */; };
		879E8AEA7D520D8C063F8435 /* Auth in Frameworks */ = {isa = PBXBuildFile; productRef = 200E4CCDAFA47C2EC9421120 /* Auth */; };
		8FDE54CB2654DC6BC4969B28 /* Customer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B317E42B44CCE47DDC6469D /* Customer.swift */; };
		93860354AF6560D09BE6A4AB /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8D7274A25915E2BCC564206E /* Preview Assets.xcassets */; };
		AC8D87D54089A576F70B6498 /* Supabase in Frameworks */ = {isa = PBXBuildFile; productRef = 7ED425A7CD271E6214F83CF7 /* Supabase */; };
		B1207DE9CE627EE51814F132 /* DashboardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDE4C7DFC5F2924793EAE058 /* DashboardViewModel.swift */; };
		BA49D22E5B472209B069AFC6 /* MainTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 86843BF751EC48DC3A58D10B /* MainTabView.swift */; };
		BDF2EA10CE56DC99E0F5174A /* CreateProductView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F6CCA54035485BCEE9751AE /* CreateProductView.swift */; };
		C09E2B02F32C38BD956653AC /* DocumentSuccessView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B182BAB2F68ECDBF0DA0227F /* DocumentSuccessView.swift */; };
		C0B66049D556F2E42DBC104C /* ProductSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 16D72A75349380E4FED9883D /* ProductSelectionView.swift */; };
		C3397F0A979258E38082D83D /* PostgREST in Frameworks */ = {isa = PBXBuildFile; productRef = 067AE68944744F83F9211F7B /* PostgREST */; };
		C43843CC575BC74A0EF1A86F /* Document.swift in Sources */ = {isa = PBXBuildFile; fileRef = 90C48718A2EA80B691EBD4A7 /* Document.swift */; };
		F03FEA1D32A9D2A7B7587E9F /* DocumentViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D586BA177898D78F2396CBCA /* DocumentViewModel.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		16D72A75349380E4FED9883D /* ProductSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductSelectionView.swift; sourceTree = "<group>"; };
		1773BEC191855B64E3F5B28A /* RegisterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterView.swift; sourceTree = "<group>"; };
		2915C2F96130682B73B68EF7 /* ErrorTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorTypes.swift; sourceTree = "<group>"; };
		2F6CCA54035485BCEE9751AE /* CreateProductView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateProductView.swift; sourceTree = "<group>"; };
		32AA44B8C53D3CFF4E355B6B /* DocumentCreationWizardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentCreationWizardView.swift; sourceTree = "<group>"; };
		3BCE9960FDBC5BB2FB74A9BB /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		425DCEA6B47516937194DF0D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		465894914B61D5E4B15E8412 /* MailComposerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MailComposerView.swift; sourceTree = "<group>"; };
		4B317E42B44CCE47DDC6469D /* Customer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Customer.swift; sourceTree = "<group>"; };
		4BB1A1EB4C446189DE4444EA /* FintechInvoice.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = FintechInvoice.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4F74AA9D812579A1A9325B51 /* FintechInvoiceApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FintechInvoiceApp.swift; sourceTree = "<group>"; };
		538C2A4EAC19941B4D1E2E8A /* DocumentItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentItem.swift; sourceTree = "<group>"; };
		5E74125F0E956FE8C1A2A65E /* DocumentSummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentSummaryView.swift; sourceTree = "<group>"; };
		6AA4DAF1FEB36486AA082CE9 /* Product.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Product.swift; sourceTree = "<group>"; };
		771770B367E8B88D4C6116A0 /* RegistrationSteps.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegistrationSteps.swift; sourceTree = "<group>"; };
		78669ED8016BF4CFE7FD95EA /* PlaceholderViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaceholderViews.swift; sourceTree = "<group>"; };
		86843BF751EC48DC3A58D10B /* MainTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabView.swift; sourceTree = "<group>"; };
		8D7274A25915E2BCC564206E /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		90C48718A2EA80B691EBD4A7 /* Document.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Document.swift; sourceTree = "<group>"; };
		9A7BD3F3A5AC7143828CD73E /* CustomerSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomerSelectionView.swift; sourceTree = "<group>"; };
		A6458A0CF4A679EF1B298D1B /* AuthenticationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationView.swift; sourceTree = "<group>"; };
		ABB108C6F494B6468D4FAF64 /* Config.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Config.plist; sourceTree = "<group>"; };
		B182BAB2F68ECDBF0DA0227F /* DocumentSuccessView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentSuccessView.swift; sourceTree = "<group>"; };
		BE7A3E6A6DC96442CE0F98EC /* EmailVerificationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmailVerificationView.swift; sourceTree = "<group>"; };
		C1343C8EBD74D328485A2BD6 /* SupabaseService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SupabaseService.swift; sourceTree = "<group>"; };
		CDE4C7DFC5F2924793EAE058 /* DashboardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardViewModel.swift; sourceTree = "<group>"; };
		D586BA177898D78F2396CBCA /* DocumentViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentViewModel.swift; sourceTree = "<group>"; };
		E2DFFBF1771BBF1758DDDEA5 /* AuthViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthViewModel.swift; sourceTree = "<group>"; };
		EA1106ED524801AD60B32D5B /* DesignSystem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DesignSystem.swift; sourceTree = "<group>"; };
		F550CD2E813D22636D5F3DBE /* CreateCustomerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateCustomerView.swift; sourceTree = "<group>"; };
		F792E77A80B2AD99FFE7D51D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B02AA95FAF07E705455AAA63 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AC8D87D54089A576F70B6498 /* Supabase in Frameworks */,
				879E8AEA7D520D8C063F8435 /* Auth in Frameworks */,
				C3397F0A979258E38082D83D /* PostgREST in Frameworks */,
				24F6891F11136EADE43B2B0C /* Storage in Frameworks */,
				2544D33545D136DE9E3A48B3 /* Realtime in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0C2A40EDD31EA92D48D2C110 /* FintechInvoice */ = {
			isa = PBXGroup;
			children = (
				425DCEA6B47516937194DF0D /* Assets.xcassets */,
				ABB108C6F494B6468D4FAF64 /* Config.plist */,
				3BCE9960FDBC5BB2FB74A9BB /* ContentView.swift */,
				4F74AA9D812579A1A9325B51 /* FintechInvoiceApp.swift */,
				F792E77A80B2AD99FFE7D51D /* Info.plist */,
				D3EF333EBBF5D30E8660B374 /* Models */,
				E303A7E1E9BA2F73903CF0E0 /* Preview Content */,
				E8B8371EB3E303ECFD82DCE3 /* Services */,
				1A87415812AE512B34FA9317 /* Utils */,
				C98EA3615F72278E6A097415 /* ViewModels */,
				CA794161000A89555AFD144C /* Views */,
			);
			path = FintechInvoice;
			sourceTree = "<group>";
		};
		1A87415812AE512B34FA9317 /* Utils */ = {
			isa = PBXGroup;
			children = (
				EA1106ED524801AD60B32D5B /* DesignSystem.swift */,
				2915C2F96130682B73B68EF7 /* ErrorTypes.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		2AFA3BC0F4B9706CDCDA0194 /* Dashboard */ = {
			isa = PBXGroup;
			children = (
				86843BF751EC48DC3A58D10B /* MainTabView.swift */,
				78669ED8016BF4CFE7FD95EA /* PlaceholderViews.swift */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		3314CAF65759957589E7F11F /* Products */ = {
			isa = PBXGroup;
			children = (
				4BB1A1EB4C446189DE4444EA /* FintechInvoice.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6449C014B30BD4249AD0EB71 /* Documents */ = {
			isa = PBXGroup;
			children = (
				F550CD2E813D22636D5F3DBE /* CreateCustomerView.swift */,
				2F6CCA54035485BCEE9751AE /* CreateProductView.swift */,
				9A7BD3F3A5AC7143828CD73E /* CustomerSelectionView.swift */,
				32AA44B8C53D3CFF4E355B6B /* DocumentCreationWizardView.swift */,
				B182BAB2F68ECDBF0DA0227F /* DocumentSuccessView.swift */,
				5E74125F0E956FE8C1A2A65E /* DocumentSummaryView.swift */,
				16D72A75349380E4FED9883D /* ProductSelectionView.swift */,
			);
			path = Documents;
			sourceTree = "<group>";
		};
		6683289D1BD9D49F3B53E147 /* Auth */ = {
			isa = PBXGroup;
			children = (
				A6458A0CF4A679EF1B298D1B /* AuthenticationView.swift */,
				BE7A3E6A6DC96442CE0F98EC /* EmailVerificationView.swift */,
				1773BEC191855B64E3F5B28A /* RegisterView.swift */,
				771770B367E8B88D4C6116A0 /* RegistrationSteps.swift */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		74A2EB84D0A7F9E55F46830E = {
			isa = PBXGroup;
			children = (
				0C2A40EDD31EA92D48D2C110 /* FintechInvoice */,
				3314CAF65759957589E7F11F /* Products */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		ACE6814A207B00BDF8AB306D /* Components */ = {
			isa = PBXGroup;
			children = (
				465894914B61D5E4B15E8412 /* MailComposerView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		C98EA3615F72278E6A097415 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				E2DFFBF1771BBF1758DDDEA5 /* AuthViewModel.swift */,
				CDE4C7DFC5F2924793EAE058 /* DashboardViewModel.swift */,
				D586BA177898D78F2396CBCA /* DocumentViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		CA794161000A89555AFD144C /* Views */ = {
			isa = PBXGroup;
			children = (
				6683289D1BD9D49F3B53E147 /* Auth */,
				ACE6814A207B00BDF8AB306D /* Components */,
				2AFA3BC0F4B9706CDCDA0194 /* Dashboard */,
				6449C014B30BD4249AD0EB71 /* Documents */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D3EF333EBBF5D30E8660B374 /* Models */ = {
			isa = PBXGroup;
			children = (
				4B317E42B44CCE47DDC6469D /* Customer.swift */,
				90C48718A2EA80B691EBD4A7 /* Document.swift */,
				538C2A4EAC19941B4D1E2E8A /* DocumentItem.swift */,
				6AA4DAF1FEB36486AA082CE9 /* Product.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		E303A7E1E9BA2F73903CF0E0 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				8D7274A25915E2BCC564206E /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		E8B8371EB3E303ECFD82DCE3 /* Services */ = {
			isa = PBXGroup;
			children = (
				C1343C8EBD74D328485A2BD6 /* SupabaseService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		733D3312D8E9C55615688C1A /* FintechInvoice */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0212224DD80BDF07559EDA6F /* Build configuration list for PBXNativeTarget "FintechInvoice" */;
			buildPhases = (
				9409D30B354512DAA70070E3 /* Sources */,
				FDD0E97923C7B22C2195B74F /* Resources */,
				B02AA95FAF07E705455AAA63 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FintechInvoice;
			packageProductDependencies = (
				7ED425A7CD271E6214F83CF7 /* Supabase */,
				200E4CCDAFA47C2EC9421120 /* Auth */,
				067AE68944744F83F9211F7B /* PostgREST */,
				005C84C91F61AF4C98003B67 /* Storage */,
				B5406BB05832354DE1B60497 /* Realtime */,
			);
			productName = FintechInvoice;
			productReference = 4BB1A1EB4C446189DE4444EA /* FintechInvoice.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3140800855D011305F807BE5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					733D3312D8E9C55615688C1A = {
						DevelopmentTeam = "";
					};
				};
			};
			buildConfigurationList = 3E635BC03616920F41E6CEA2 /* Build configuration list for PBXProject "FintechInvoice" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = 74A2EB84D0A7F9E55F46830E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */,
			);
			preferredProjectObjectVersion = 77;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				733D3312D8E9C55615688C1A /* FintechInvoice */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FDD0E97923C7B22C2195B74F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7CE3644E450CD59E6E3D53A5 /* Assets.xcassets in Resources */,
				46EDED4198FE610241DF5F42 /* Config.plist in Resources */,
				93860354AF6560D09BE6A4AB /* Preview Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9409D30B354512DAA70070E3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				494F932B33D77944969B92F6 /* AuthViewModel.swift in Sources */,
				6A2BBBAA14F2C841BD66D042 /* AuthenticationView.swift in Sources */,
				284DA0D6ABC0E90455ADD27C /* ContentView.swift in Sources */,
				29A2E909743D24A504E7F82F /* CreateCustomerView.swift in Sources */,
				BDF2EA10CE56DC99E0F5174A /* CreateProductView.swift in Sources */,
				8FDE54CB2654DC6BC4969B28 /* Customer.swift in Sources */,
				0F9CF5713392CB926249549D /* CustomerSelectionView.swift in Sources */,
				B1207DE9CE627EE51814F132 /* DashboardViewModel.swift in Sources */,
				36CB2BE71B2F3D2B0062DA96 /* DesignSystem.swift in Sources */,
				C43843CC575BC74A0EF1A86F /* Document.swift in Sources */,
				4D41A3DBCD63B66B220687AA /* DocumentCreationWizardView.swift in Sources */,
				5115DDA2EDC2F092BB9CEB9C /* DocumentItem.swift in Sources */,
				C09E2B02F32C38BD956653AC /* DocumentSuccessView.swift in Sources */,
				6254EF8EDF32EE4844A7B55A /* DocumentSummaryView.swift in Sources */,
				F03FEA1D32A9D2A7B7587E9F /* DocumentViewModel.swift in Sources */,
				21D36FCBC62D153DD6A2722E /* EmailVerificationView.swift in Sources */,
				110528DC201466540BB2F43B /* ErrorTypes.swift in Sources */,
				62761EC8E399A0BBFE635EC2 /* FintechInvoiceApp.swift in Sources */,
				82F57E5B82F7C5A46347A7E5 /* MailComposerView.swift in Sources */,
				BA49D22E5B472209B069AFC6 /* MainTabView.swift in Sources */,
				1807F7C98ECDFB17D71DED34 /* PlaceholderViews.swift in Sources */,
				5F44A5BBCF494591736DC396 /* Product.swift in Sources */,
				C0B66049D556F2E42DBC104C /* ProductSelectionView.swift in Sources */,
				0EDEE960F296DACBFF57FC4A /* RegisterView.swift in Sources */,
				82477D2D4E74068EB7EB1E55 /* RegistrationSteps.swift in Sources */,
				0319D8B5963CD77A4B657B93 /* SupabaseService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3D827DD8168F39335EF409F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		73BC53E4952FDA8DF52ED8F9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		D4B181B9C702A37374D2DCCC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				DEVELOPMENT_ASSET_PATHS = "FintechInvoice/Preview\\ Content";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = FintechInvoice/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.fintech.invoice.app;
				SDKROOT = iphoneos;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		FCE3B58A1C7F88AC26A9F395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				DEVELOPMENT_ASSET_PATHS = "FintechInvoice/Preview\\ Content";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = FintechInvoice/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.fintech.invoice.app;
				SDKROOT = iphoneos;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0212224DD80BDF07559EDA6F /* Build configuration list for PBXNativeTarget "FintechInvoice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D4B181B9C702A37374D2DCCC /* Debug */,
				FCE3B58A1C7F88AC26A9F395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		3E635BC03616920F41E6CEA2 /* Build configuration list for PBXProject "FintechInvoice" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3D827DD8168F39335EF409F7 /* Debug */,
				73BC53E4952FDA8DF52ED8F9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/supabase/supabase-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		005C84C91F61AF4C98003B67 /* Storage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Storage;
		};
		067AE68944744F83F9211F7B /* PostgREST */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = PostgREST;
		};
		200E4CCDAFA47C2EC9421120 /* Auth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Auth;
		};
		7ED425A7CD271E6214F83CF7 /* Supabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Supabase;
		};
		B5406BB05832354DE1B60497 /* Realtime */ = {
			isa = XCSwiftPackageProductDependency;
			package = 65A3CBEC1799E75510B78611 /* XCRemoteSwiftPackageReference "supabase-swift" */;
			productName = Realtime;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3140800855D011305F807BE5 /* Project object */;
}
