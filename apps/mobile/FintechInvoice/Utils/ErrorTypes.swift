import Foundation
import MessageUI

// MARK: - Mail Error Type
enum MailError: Error, Equatable {
    case composerUnavailable
    case sendingFailed(String)
    case unknown
    
    var localizedDescription: String {
        switch self {
        case .composerUnavailable:
            return "Mail composer is not available"
        case .sendingFailed(let message):
            return "Failed to send email: \(message)"
        case .unknown:
            return "Unknown mail error occurred"
        }
    }
}

// MARK: - Document Error Type
enum DocumentError: Error, Equatable {
    case invalidData(String)
    case creationFailed(String)
    case notFound
    case unauthorized
    case networkError(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidData(let message):
            return "Invalid data: \(message)"
        case .creationFailed(let message):
            return "Document creation failed: \(message)"
        case .notFound:
            return "Document not found"
        case .unauthorized:
            return "Unauthorized access"
        case .networkError(let message):
            return "Network error: \(message)"
        }
    }
}

// MARK: - Customer Error Type
enum CustomerError: Error, Equatable {
    case invalidData(String)
    case creationFailed(String)
    case notFound
    case duplicateVatId
    case networkError(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidData(let message):
            return "Invalid customer data: \(message)"
        case .creationFailed(let message):
            return "Customer creation failed: \(message)"
        case .notFound:
            return "Customer not found"
        case .duplicateVatId:
            return "Customer with this VAT ID already exists"
        case .networkError(let message):
            return "Network error: \(message)"
        }
    }
}
