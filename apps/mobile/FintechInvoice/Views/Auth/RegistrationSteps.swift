import SwiftUI
import UIKit

// MARK: - Step 1: Industry and Revenue
struct IndustryStep: View {
    @ObservedObject var data: RegistrationFormData
    
    private let industries = [
        ("technology", "טכנולוגיה"),
        ("retail", "קמעונאות"),
        ("services", "שירותים"),
        ("manufacturing", "ייצור"),
        ("construction", "בנייה"),
        ("healthcare", "בריאות"),
        ("other", "אחר")
    ]
    
    private let revenueRanges = [
        ("0-50K", "0-50 אלף ₪"),
        ("50K-100K", "50-100 אלף ₪"),
        ("100K-250K", "100-250 אלף ₪"),
        ("250K-500K", "250-500 אלף ₪"),
        ("500K-1M", "500 אלף - 1 מיליון ₪"),
        ("1M-2.5M", "1-2.5 מיליון ₪"),
        ("2.5M-5M", "2.5-5 מיליון ₪"),
        ("5M+", "מעל 5 מיליון ₪")
    ]
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            Text("פרטי עסק בסיסיים")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)
            
            VStack(alignment: .trailing, spacing: .spacing4) {
                // Industry Selection
                VStack(alignment: .trailing, spacing: .spacing2) {
                    Text("תחום עיסוק")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                    
                    Menu {
                        ForEach(industries, id: \.0) { industry in
                            Button(industry.1) {
                                DispatchQueue.main.async {
                                    data.industry = industry.0
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(industries.first(where: { $0.0 == data.industry })?.1 ?? "בחר תחום")
                                .foregroundColor(data.industry.isEmpty ? .mutedForeground : .cardForeground)

                            Spacer()

                            Image(systemName: "chevron.down")
                                .foregroundColor(.mutedForeground)
                        }
                        .padding(.horizontal, .spacing4)
                        .padding(.vertical, .spacing3)
                        .background(Color.inputBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: .radiusDefault)
                                .stroke(Color.border, lineWidth: 1)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                    }
                }
                
                // Annual Revenue Selection
                VStack(alignment: .trailing, spacing: .spacing2) {
                    Text("מחזור שנתי")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                    
                    Menu {
                        ForEach(revenueRanges, id: \.0) { range in
                            Button(range.1) {
                                DispatchQueue.main.async {
                                    data.annualRevenue = range.0
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(revenueRanges.first(where: { $0.0 == data.annualRevenue })?.1 ?? "בחר טווח")
                                .foregroundColor(data.annualRevenue.isEmpty ? .mutedForeground : .cardForeground)

                            Spacer()

                            Image(systemName: "chevron.down")
                                .foregroundColor(.mutedForeground)
                        }
                        .padding(.horizontal, .spacing4)
                        .padding(.vertical, .spacing3)
                        .background(Color.inputBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: .radiusDefault)
                                .stroke(Color.border, lineWidth: 1)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                    }
                }
            }
        }
    }
}

// MARK: - Step 2: Business Details
struct BusinessDetailsStep: View {
    @ObservedObject var data: RegistrationFormData
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            Text("פרטי החברה")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)
            
            VStack(alignment: .trailing, spacing: .spacing4) {
                CosmicTextFieldFixed(
                    title: "ע.מ / ח.פ",
                    text: $data.companyId,
                    placeholder: "*********",
                    keyboardType: .numberPad
                )

                CosmicTextFieldFixed(
                    title: "שם העסק (עברית)",
                    text: $data.nameHebrew,
                    placeholder: "שם החברה בעברית"
                )

                CosmicTextFieldFixed(
                    title: "שם העסק (אנגלית)",
                    text: $data.nameEnglish,
                    placeholder: "Company Name in English",
                    isOptional: true
                )

                CosmicTextFieldFixed(
                    title: "כתובת",
                    text: $data.addressHebrew,
                    placeholder: "רחוב, מספר בית, עיר"
                )

                CosmicTextFieldFixed(
                    title: "עיר",
                    text: $data.cityHebrew,
                    placeholder: "תל אביב"
                )
            }
        }
        .dismissKeyboardOnTap()
    }
}

// MARK: - Step 3: Account Details
struct AccountDetailsStep: View {
    @ObservedObject var data: RegistrationFormData
    @State private var showingPassword = false
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            Text("פרטי החשבון")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)
            
            VStack(alignment: .trailing, spacing: .spacing4) {
                CosmicTextFieldFixed(
                    title: "כתובת אימייל",
                    text: $data.email,
                    placeholder: "<EMAIL>",
                    keyboardType: .emailAddress,
                    autocapitalizationType: .none
                )
                
                // Password Field with Toggle
                VStack(alignment: .trailing, spacing: .spacing2) {
                    Text("סיסמה")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                    
                    HStack {
                        Button(action: { showingPassword.toggle() }) {
                            Image(systemName: showingPassword ? "eye.slash" : "eye")
                                .foregroundColor(.mutedForeground)
                        }
                        
                        if showingPassword {
                            TextField("••••••••", text: $data.password)
                                .multilineTextAlignment(.trailing)
                        } else {
                            SecureField("••••••••", text: $data.password)
                                .multilineTextAlignment(.trailing)
                        }
                    }
                    .padding(.horizontal, .spacing4)
                    .padding(.vertical, .spacing3)
                    .background(Color.inputBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusDefault)
                            .stroke(Color.border, lineWidth: 1)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                }
                
                CosmicTextFieldFixed(
                    title: "שם מלא",
                    text: $data.fullName,
                    placeholder: "שם פרטי ומשפחה"
                )

                CosmicTextFieldFixed(
                    title: "טלפון",
                    text: $data.phone,
                    placeholder: "050-1234567",
                    keyboardType: .phonePad
                )
            }
        }
        .dismissKeyboardOnTap()
    }
}

// MARK: - Survey Steps
struct AccountingServicesStep: View {
    @ObservedObject var data: RegistrationFormData
    
    var body: some View {
        SurveyStepView(
            title: "שירותי הנהלת חשבונות",
            description: "האם תהיה מעוניין לשמוע הצעה ייחודית לשירותי הנהלת חשבונות?",
            isSelected: $data.interestedInAccounting
        )
    }
}

struct InsuranceStep: View {
    @ObservedObject var data: RegistrationFormData
    
    var body: some View {
        SurveyStepView(
            title: "ביטוח עסקי",
            description: "האם תהיה מעוניין לשמוע לגבי הצעה מיוחדת לביטוח העסק?",
            isSelected: $data.interestedInInsurance
        )
    }
}

struct LoanStep: View {
    @ObservedObject var data: RegistrationFormData
    
    var body: some View {
        SurveyStepView(
            title: "הלוואות עסקיות",
            description: "האם תהיה מעוניין לשמוע הצעה ייחודית להלוואות עסקיות?",
            isSelected: $data.interestedInLoan
        )
    }
}

// MARK: - Reusable Components
struct CosmicTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    var isOptional: Bool = false
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            HStack {
                if isOptional {
                    Text("(אופציונלי)")
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
                
                Spacer()
                
                Text(title)
                    .font(.hebrewCaption)
                    .foregroundColor(.cardForeground)
            }
            
            TextField(placeholder, text: $text)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }
}

struct SurveyStepView: View {
    let title: String
    let description: String
    @Binding var isSelected: Bool
    
    var body: some View {
        VStack(spacing: .spacing6) {
            VStack(spacing: .spacing4) {
                Text(title)
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text(description)
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
            }
            
            HStack(spacing: .spacing4) {
                Button("לא") {
                    DispatchQueue.main.async {
                        isSelected = false
                    }
                }
                .buttonStyle(SurveyButtonStyle(isSelected: !isSelected))

                Button("כן") {
                    DispatchQueue.main.async {
                        isSelected = true
                    }
                }
                .buttonStyle(SurveyButtonStyle(isSelected: isSelected))
            }
        }
    }
}

struct SurveyButtonStyle: ButtonStyle {
    let isSelected: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.hebrewBody.weight(.medium))
            .foregroundColor(isSelected ? .primaryForeground : .cardForeground)
            .padding(.horizontal, .spacing8)
            .padding(.vertical, .spacing3)
            .background(isSelected ? Color.primary : Color.secondary)
            .overlay(
                RoundedRectangle(cornerRadius: .radiusDefault)
                    .stroke(isSelected ? Color.primary : Color.border, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
