import SwiftUI

struct CreateProductView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView(.vertical, showsIndicators: true) {
                VStack(alignment: .trailing, spacing: .spacing6) {
                    // Header
                    VStack(alignment: .trailing, spacing: .spacing3) {
                        Text("פריט חדש")
                            .font(.hebrewHeading.weight(.semibold))
                            .foregroundColor(.cardForeground)

                        Text("מלא את פרטי הפריט החדש")
                            .font(.hebrewBody)
                            .foregroundColor(.mutedForeground)
                    }
                    
                    // Form Fields
                    VStack(spacing: .spacing4) {
                        
                        // Product Name
                        VStack(alignment: .trailing, spacing: .spacing2) {
                            Text("שם הפריט *")
                                .font(.hebrewCaption.weight(.medium))
                                .foregroundColor(.cardForeground)

                            TextField("הכנס שם", text: $documentViewModel.productFormData.nameHebrew)
                                .textFieldStyle(CosmicTextFieldStyle())
                                .multilineTextAlignment(.trailing)

                            if let error = documentViewModel.productFormData.nameError {
                                Text(error)
                                    .font(.caption2)
                                    .foregroundColor(.destructive)
                            }
                        }

                        // Description
                        VStack(alignment: .trailing, spacing: .spacing2) {
                            Text("תיאור")
                                .font(.hebrewCaption.weight(.medium))
                                .foregroundColor(.cardForeground)

                            TextField("תיאור הפריט", text: $documentViewModel.productFormData.descriptionHebrew)
                                .textFieldStyle(CosmicTextFieldStyle())
                                .multilineTextAlignment(.trailing)
                                .lineLimit(3)
                        }
                        
                        // Price
                        VStack(alignment: .trailing, spacing: .spacing2) {
                            Text("מחיר יחידה *")
                                .font(.hebrewCaption.weight(.medium))
                                .foregroundColor(.cardForeground)
                            
                            HStack {
                                Text("₪")
                                    .font(.hebrewBody)
                                    .foregroundColor(.mutedForeground)
                                
                                TextField("0.00", text: $documentViewModel.productFormData.unitPrice)
                                    .textFieldStyle(CosmicTextFieldStyle())
                                    .keyboardType(.decimalPad)
                                    .multilineTextAlignment(.trailing)
                            }
                            
                            if let error = documentViewModel.productFormData.priceError {
                                Text(error)
                                    .font(.caption2)
                                    .foregroundColor(.destructive)
                            }
                        }
                        
                        // VAT Rate
                        VStack(alignment: .trailing, spacing: .spacing2) {
                            Text("שיעור מע״מ")
                                .font(.hebrewCaption.weight(.medium))
                                .foregroundColor(.cardForeground)
                            
                            VStack(spacing: .spacing2) {
                                // Preset VAT rates
                                HStack(spacing: .spacing3) {
                                    ForEach(VATRate.allCases, id: \.self) { vatRate in
                                        Button(vatRate.displayName) {
                                            documentViewModel.productFormData.vatRate = vatRate.rawValue
                                        }
                                        .buttonStyle(ToggleButtonStyle(isSelected: documentViewModel.productFormData.vatRate == vatRate.rawValue))
                                        .font(.caption)
                                    }
                                    Spacer()
                                }
                                
                                // Custom VAT rate input
                                HStack {
                                    Text("%")
                                        .font(.hebrewBody)
                                        .foregroundColor(.mutedForeground)
                                    
                                    TextField("18.0", value: $documentViewModel.productFormData.vatRate, format: .number)
                                        .textFieldStyle(CosmicTextFieldStyle())
                                        .keyboardType(.decimalPad)
                                        .multilineTextAlignment(.trailing)
                                        .frame(width: 80)
                                    
                                    Spacer()
                                    
                                    Text("מע״מ מותאם אישית:")
                                        .font(.caption)
                                        .foregroundColor(.mutedForeground)
                                }
                            }
                            
                            if let error = documentViewModel.productFormData.vatRateError {
                                Text(error)
                                    .font(.caption2)
                                    .foregroundColor(.destructive)
                            }
                        }
                        
                        // Preview
                        VStack(alignment: .trailing, spacing: .spacing3) {
                            Text("תצוגה מקדימה")
                                .font(.hebrewCaption.weight(.medium))
                                .foregroundColor(.cardForeground)
                            
                            HStack {
                                VStack(alignment: .leading, spacing: .spacing1) {
                                    Text("מע״מ: \(String(format: "%.1f", documentViewModel.productFormData.vatRate))%")
                                        .font(.caption2)
                                        .foregroundColor(.mutedForeground)
                                }
                                
                                Spacer()
                                
                                VStack(alignment: .trailing, spacing: .spacing1) {
                                    Text(documentViewModel.productFormData.nameHebrew.isEmpty ? "שם הפריט" : documentViewModel.productFormData.nameHebrew)
                                        .font(.hebrewBody.weight(.medium))
                                        .foregroundColor(.cardForeground)
                                        .multilineTextAlignment(.trailing)
                                    
                                    if !documentViewModel.productFormData.descriptionHebrew.isEmpty {
                                        Text(documentViewModel.productFormData.descriptionHebrew)
                                            .font(.caption2)
                                            .foregroundColor(.mutedForeground)
                                            .multilineTextAlignment(.trailing)
                                            .lineLimit(2)
                                    }
                                    
                                    Text("₪\(documentViewModel.productFormData.unitPrice.isEmpty ? "0.00" : documentViewModel.productFormData.unitPrice)")
                                        .font(.hebrewCaption.weight(.semibold))
                                        .foregroundColor(.primary)
                                }
                            }
                            .padding(.spacing3)
                            .background(Color.muted.opacity(0.3))
                            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                        }
                    }
                }
                .padding(.spacing4)
            }
            .background(Color.background)
            .navigationTitle("פריט חדש")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarBackButtonHidden(true)
            .navigationBarItems(
                leading: Button("ביטול") {
                    dismiss()
                },
                trailing: Button("שמור") {
                    documentViewModel.createProduct()
                }
                .disabled(!documentViewModel.productFormData.isValid || documentViewModel.isLoading)
            )
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Toggle Button Style
struct ToggleButtonStyle: ButtonStyle {
    let isSelected: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.hebrewCaption.weight(.medium))
            .foregroundColor(isSelected ? .primary : .mutedForeground)
            .padding(.horizontal, .spacing3)
            .padding(.vertical, .spacing2)
            .background(
                RoundedRectangle(cornerRadius: .radiusSmall)
                    .fill(isSelected ? Color.primary.opacity(0.1) : Color.muted.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusSmall)
                            .stroke(isSelected ? Color.primary : Color.clear, lineWidth: 1)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    CreateProductView(documentViewModel: DocumentViewModel())
}
