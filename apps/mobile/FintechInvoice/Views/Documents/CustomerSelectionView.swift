import SwiftUI

struct CustomerSelectionView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            // Header
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("בחר לקוח")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("בחר לקוח קיים או צור לקוח חדש")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.mutedForeground)
                
                TextField("חפש לקוח...", text: $documentViewModel.customerSearchQuery)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.hebrewBody)
            }
            .padding(.spacing3)
            .background(Color.muted.opacity(0.3))
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
            
            // Create New Customer Button
            Button("צור לקוח חדש") {
                documentViewModel.showingCreateCustomer = true
            }
            .buttonStyle(CosmicSecondaryButtonStyle())
            
            // Customer List
            if documentViewModel.isLoading {
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                    Text("טוען לקוחות...")
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else if documentViewModel.filteredCustomers.isEmpty {
                VStack(spacing: .spacing4) {
                    Image(systemName: "person.2")
                        .font(.system(size: 48))
                        .foregroundColor(.mutedForeground)
                    
                    Text(documentViewModel.customerSearchQuery.isEmpty ? "אין לקוחות" : "לא נמצאו לקוחות")
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.mutedForeground)
                    
                    Text(documentViewModel.customerSearchQuery.isEmpty ? 
                         "צור לקוח חדש כדי להתחיל" : 
                         "נסה חיפוש אחר או צור לקוח חדש")
                        .font(.hebrewCaption)
                        .foregroundColor(.mutedForeground)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else {
                LazyVStack(spacing: .spacing3) {
                    ForEach(documentViewModel.filteredCustomers) { customer in
                        CustomerRowView(
                            customer: customer,
                            isSelected: documentViewModel.selectedCustomer?.id == customer.id
                        ) {
                            documentViewModel.selectedCustomer = customer
                        }
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Customer Row View
struct CustomerRowView: View {
    let customer: Customer
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: .spacing3) {
                VStack(alignment: .leading, spacing: .spacing1) {
                    if customer.hasContactInfo {
                        HStack(spacing: .spacing2) {
                            if customer.contactEmail != nil {
                                Image(systemName: "envelope")
                                    .font(.caption2)
                                    .foregroundColor(.mutedForeground)
                            }
                            if customer.contactPhone != nil {
                                Image(systemName: "phone")
                                    .font(.caption2)
                                    .foregroundColor(.mutedForeground)
                            }
                        }
                    }
                    
                    Text(customer.formattedBusinessNumber)
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(customer.displayName)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)
                    
                    Text(customer.fullAddress)
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                        .multilineTextAlignment(.trailing)
                        .lineLimit(2)
                    
                    if let vatId = customer.formattedVatId {
                        Text("ע.מ: \(vatId)")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }
                }
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.primary)
                        .font(.title3)
                }
            }
            .padding(.spacing3)
            .background(
                RoundedRectangle(cornerRadius: .radiusSmall)
                    .fill(isSelected ? Color.primary.opacity(0.1) : Color.muted.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusSmall)
                            .stroke(isSelected ? Color.primary : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



#Preview {
    CustomerSelectionView(documentViewModel: DocumentViewModel())
}
