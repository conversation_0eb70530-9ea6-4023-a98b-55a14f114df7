import SwiftUI

struct CreateCustomerView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: .spacing6) {
                    headerSection
                    formSection
                    buttonSection
                }
                .padding(.spacing4)
            }
            .background(Color.background)
            .navigationTitle("לקוח חדש")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }

    private var headerSection: some View {
        VStack(alignment: .trailing, spacing: .spacing3) {
            Text("יצירת לקוח חדש")
                .font(.hebrewHeading.weight(.semibold))
                .foregroundColor(.cardForeground)

            Text("מלא את פרטי הלקוח החדש")
                .font(.hebrewBody)
                .foregroundColor(.mutedForeground)
                .multilineTextAlignment(.trailing)
        }
    }

    private var formSection: some View {
        LazyVStack(spacing: .spacing4) {
            companyNameField
            businessNumberField
            addressField
            cityField
            emailField
            phoneField
            notesField
        }
    }

    private var companyNameField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("שם החברה *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("הכנס שם חברה", text: $documentViewModel.customerFormData.nameHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var businessNumberField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("ע.מ / ח.פ *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("*********", text: $documentViewModel.customerFormData.businessNumber)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.numberPad)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.businessNumberError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }



    private var addressField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("כתובת *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("רחוב 123", text: $documentViewModel.customerFormData.billingAddressHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var cityField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("עיר *")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("תל אביב", text: $documentViewModel.customerFormData.cityHebrew)
                .textFieldStyle(CosmicTextFieldStyle())
                .multilineTextAlignment(.trailing)
        }
    }

    private var emailField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("אימייל")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("<EMAIL>", text: $documentViewModel.customerFormData.contactEmail)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.emailError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var phoneField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("טלפון")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            TextField("050-1234567", text: $documentViewModel.customerFormData.contactPhone)
                .textFieldStyle(CosmicTextFieldStyle())
                .keyboardType(.phonePad)
                .multilineTextAlignment(.trailing)

            if let error = documentViewModel.customerFormData.phoneError {
                Text(error)
                    .font(.hebrewCaption)
                    .foregroundColor(.destructive)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var notesField: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            Text("הערות")
                .font(.hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)

            if #available(iOS 16.0, *) {
                TextField("הערות נוספות", text: $documentViewModel.customerFormData.notes, axis: .vertical)
                    .textFieldStyle(CosmicTextFieldStyle())
                    .lineLimit(3...6)
                    .multilineTextAlignment(.trailing)
            } else {
                TextField("הערות נוספות", text: $documentViewModel.customerFormData.notes)
                    .textFieldStyle(CosmicTextFieldStyle())
                    .lineLimit(3)
                    .multilineTextAlignment(.trailing)
            }
        }
    }

    private var buttonSection: some View {
        HStack(spacing: .spacing4) {
            Button("ביטול") {
                dismiss()
            }
            .buttonStyle(CosmicSecondaryButtonStyle())
            .frame(maxWidth: .infinity)

            Button("צור לקוח") {
                documentViewModel.createCustomer()
            }
            .buttonStyle(CosmicPrimaryButtonStyle())
            .frame(maxWidth: .infinity)
            .disabled(!documentViewModel.customerFormData.isValid)
        }
        .padding(.top, .spacing4)
    }
}



#Preview {
    CreateCustomerView(documentViewModel: DocumentViewModel())
}
