import SwiftUI

struct ProductSelectionView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            // Header
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("בחר מוצרים ושירותים")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("הוסף מוצרים ושירותים למסמך")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.mutedForeground)
                
                TextField("חפש מוצר או שירות...", text: $documentViewModel.productSearchQuery)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.hebrewBody)
            }
            .padding(.spacing3)
            .background(Color.muted.opacity(0.3))
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
            
            // Create New Product Button
            Button("צור מוצר/שירות חדש") {
                documentViewModel.showingCreateProduct = true
            }
            .buttonStyle(CosmicSecondaryButtonStyle())
            
            // Selected Products Section
            if !documentViewModel.selectedProducts.isEmpty {
                VStack(alignment: .trailing, spacing: .spacing3) {
                    Text("מוצרים נבחרים (\(documentViewModel.selectedProducts.count))")
                        .font(.hebrewBody.weight(.semibold))
                        .foregroundColor(.cardForeground)
                    
                    LazyVStack(spacing: .spacing3) {
                        ForEach(Array(documentViewModel.selectedProducts.enumerated()), id: \.element.id) { index, item in
                            SelectedProductRowView(
                                item: item,
                                index: index,
                                documentViewModel: documentViewModel
                            )
                        }
                    }
                    
                    // Summary
                    VStack(spacing: .spacing2) {
                        Divider()
                        
                        HStack {
                            Text(documentViewModel.documentSummary.formattedTotal)
                                .font(.hebrewBody.weight(.semibold))
                                .foregroundColor(.cardForeground)
                            
                            Spacer()
                            
                            Text("סה״כ:")
                                .font(.hebrewBody.weight(.medium))
                                .foregroundColor(.cardForeground)
                        }
                    }
                    .padding(.spacing3)
                    .background(Color.muted.opacity(0.3))
                    .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
                }
                .padding(.spacing3)
                .cosmicCard()
            }
            
            // Available Products List
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("מוצרים ושירותים זמינים")
                    .font(.hebrewBody.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                if documentViewModel.isLoading {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("טוען מוצרים...")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                    }
                    .frame(maxWidth: .infinity, minHeight: 150)
                } else if documentViewModel.filteredProducts.isEmpty {
                    VStack(spacing: .spacing4) {
                        Image(systemName: "cube.box")
                            .font(.system(size: 48))
                            .foregroundColor(.mutedForeground)
                        
                        Text(documentViewModel.productSearchQuery.isEmpty ? "אין מוצרים" : "לא נמצאו מוצרים")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.mutedForeground)
                        
                        Text(documentViewModel.productSearchQuery.isEmpty ? 
                             "צור מוצר או שירות חדש כדי להתחיל" : 
                             "נסה חיפוש אחר או צור מוצר חדש")
                            .font(.hebrewCaption)
                            .foregroundColor(.mutedForeground)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity, minHeight: 150)
                } else {
                    LazyVStack(spacing: .spacing3) {
                        ForEach(documentViewModel.filteredProducts) { product in
                            AvailableProductRowView(product: product) {
                                documentViewModel.addProduct(product)
                            }
                        }
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
    }
}

// MARK: - Selected Product Row View
struct SelectedProductRowView: View {
    let item: DocumentItemFormData
    let index: Int
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(spacing: .spacing3) {
            HStack {
                Button(action: {
                    documentViewModel.removeProduct(at: index)
                }) {
                    Image(systemName: "trash")
                        .foregroundColor(.destructive)
                        .font(.caption)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(item.descriptionHebrew)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)
                    
                    if let product = item.selectedProduct {
                        Text(product.typeDisplayName)
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }
                }
            }
            
            // Quantity and Price inputs
            HStack(spacing: .spacing3) {
                // Discount
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text("הנחה %")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                    
                    TextField("0", text: Binding(
                        get: { documentViewModel.selectedProducts[index].discountPercent },
                        set: { documentViewModel.updateProductDiscount(at: index, discount: $0) }
                    ))
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
                    .frame(width: 60)
                }
                
                // Price
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text("מחיר")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                    
                    TextField("0.00", text: Binding(
                        get: { documentViewModel.selectedProducts[index].unitPrice },
                        set: { documentViewModel.updateProductPrice(at: index, price: $0) }
                    ))
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
                    .frame(width: 80)
                }
                
                // Quantity
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text("כמות")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                    
                    TextField("1", text: Binding(
                        get: { documentViewModel.selectedProducts[index].quantity },
                        set: { documentViewModel.updateProductQuantity(at: index, quantity: $0) }
                    ))
                    .textFieldStyle(CosmicTextFieldStyle())
                    .keyboardType(.decimalPad)
                    .frame(width: 60)
                }
                
                Spacer()
                
                // Total
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text("סה״כ")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                    
                    Text(item.formattedTotalWithVat)
                        .font(.hebrewCaption.weight(.semibold))
                        .foregroundColor(.cardForeground)
                }
            }
        }
        .padding(.spacing3)
        .background(Color.muted.opacity(0.3))
        .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
    }
}

// MARK: - Available Product Row View
struct AvailableProductRowView: View {
    let product: Product
    let onAdd: () -> Void
    
    var body: some View {
        Button(action: onAdd) {
            HStack(spacing: .spacing3) {
                Image(systemName: "plus.circle")
                    .foregroundColor(.primary)
                    .font(.title3)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(product.displayName)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)
                    
                    if let description = product.descriptionHebrew, !description.isEmpty {
                        Text(description)
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                            .multilineTextAlignment(.trailing)
                            .lineLimit(2)
                    }
                    
                    HStack(spacing: .spacing2) {
                        Text(product.formattedVatRate)
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text("•")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text(product.typeDisplayName)
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text("•")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text(product.formattedPrice)
                            .font(.caption2.weight(.medium))
                            .foregroundColor(.cardForeground)
                    }
                }
            }
            .padding(.spacing3)
            .background(Color.muted.opacity(0.3))
            .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProductSelectionView(documentViewModel: DocumentViewModel())
}
