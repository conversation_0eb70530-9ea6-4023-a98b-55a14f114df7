import SwiftUI

struct DocumentSuccessView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: .spacing6) {
                Spacer()
                
                // Success Animation
                VStack(spacing: .spacing4) {
                    ZStack {
                        Circle()
                            .fill(Color.success.opacity(0.1))
                            .frame(width: 120, height: 120)
                        
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.success)
                    }
                    
                    VStack(spacing: .spacing2) {
                        Text("מסמך נוצר בהצלחה!")
                            .font(.hebrewHeading.weight(.bold))
                            .foregroundColor(.cardForeground)
                            .multilineTextAlignment(.center)
                        
                        if let document = documentViewModel.createdDocument {
                            Text("מסמך מספר \(document.documentNumber)")
                                .font(.hebrewBody)
                                .foregroundColor(.mutedForeground)
                            
                            Text("בסך \(document.formattedTotal)")
                                .font(.hebrewHeading.weight(.semibold))
                                .foregroundColor(.primary)
                        }
                    }
                }
                
                Spacer()
                
                // Document Details Card
                if let document = documentViewModel.createdDocument,
                   let customer = documentViewModel.selectedCustomer {
                    VStack(alignment: .trailing, spacing: .spacing4) {
                        Text("פרטי המסמך")
                            .font(.hebrewBody.weight(.semibold))
                            .foregroundColor(.cardForeground)
                        
                        VStack(spacing: .spacing3) {
                            DocumentDetailRow(
                                title: "סוג מסמך:",
                                value: document.documentType.displayName
                            )
                            
                            DocumentDetailRow(
                                title: "מספר מסמך:",
                                value: document.documentNumber
                            )
                            
                            DocumentDetailRow(
                                title: "לקוח:",
                                value: customer.displayName
                            )
                            
                            DocumentDetailRow(
                                title: "תאריך:",
                                value: document.formattedIssueDate
                            )
                            
                            DocumentDetailRow(
                                title: "סטטוס:",
                                value: document.status.displayName
                            )
                            
                            Divider()
                            
                            DocumentDetailRow(
                                title: "סה״כ:",
                                value: document.formattedTotal,
                                isTotal: true
                            )
                        }
                    }
                    .padding(.spacing4)
                    .cosmicCard()
                }
                
                // Action Buttons
                VStack(spacing: .spacing3) {
                    Text("שתף את המסמך")
                        .font(.hebrewBody.weight(.semibold))
                        .foregroundColor(.cardForeground)
                    
                    VStack(spacing: .spacing3) {
                        // Email Button
                        if let document = documentViewModel.createdDocument,
                           let customer = documentViewModel.selectedCustomer {
                            MailComposerButton(document: document, customer: customer)
                        }

                        // WhatsApp Button
                        Button(action: {
                            documentViewModel.shareViaWhatsApp()
                        }) {
                            HStack {
                                Image(systemName: "message.fill")
                                    .foregroundColor(.white)

                                Text("שלח בוואטסאפ")
                                    .font(.hebrewBody.weight(.medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.spacing3)
                            .background(Color.green)
                            .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                        }
                        
                        // WhatsApp Business Button
                        Button(action: {
                            documentViewModel.shareViaWhatsAppBusiness()
                        }) {
                            HStack {
                                Image(systemName: "briefcase.fill")
                                    .foregroundColor(.white)
                                
                                Text("שלח בוואטסאפ עסקי")
                                    .font(.hebrewBody.weight(.medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.spacing3)
                            .background(Color.green.opacity(0.8))
                            .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                        }
                        
                        // Download PDF Button
                        Button(action: {
                            documentViewModel.downloadPDF()
                        }) {
                            HStack {
                                Image(systemName: "arrow.down.doc.fill")
                                    .foregroundColor(.primary)
                                
                                Text("הורד PDF")
                                    .font(.hebrewBody.weight(.medium))
                                    .foregroundColor(.primary)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.spacing3)
                            .background(Color.primary.opacity(0.1))
                            .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
                        }
                    }
                }
                
                Spacer()
                
                // Done Button
                Button("סיום") {
                    documentViewModel.resetWizard()
                    dismiss()
                }
                .buttonStyle(CosmicPrimaryButtonStyle())
            }
            .padding(.spacing4)
            .background(Color.background)
            .navigationTitle("מסמך נוצר")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("סגור") {
                        documentViewModel.resetWizard()
                        dismiss()
                    }
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .interactiveDismissDisabled() // Prevent swipe to dismiss
    }
}

// MARK: - Document Detail Row
struct DocumentDetailRow: View {
    let title: String
    let value: String
    let isTotal: Bool
    
    init(title: String, value: String, isTotal: Bool = false) {
        self.title = title
        self.value = value
        self.isTotal = isTotal
    }
    
    var body: some View {
        HStack {
            Text(value)
                .font(isTotal ? .hebrewHeading.weight(.bold) : .hebrewBody)
                .foregroundColor(isTotal ? .primary : .cardForeground)
            
            Spacer()
            
            Text(title)
                .font(isTotal ? .hebrewBody.weight(.semibold) : .hebrewBody.weight(.medium))
                .foregroundColor(.cardForeground)
        }
    }
}

// MARK: - Success Animation View
struct SuccessAnimationView: View {
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0
    
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.success.opacity(0.1))
                .frame(width: 120, height: 120)
                .scaleEffect(scale)
                .opacity(opacity)
            
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.success)
                .scaleEffect(scale)
                .opacity(opacity)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                scale = 1.0
                opacity = 1.0
            }
        }
    }
}

// MARK: - Sharing Helper Extension
extension DocumentViewModel {
    func canOpenWhatsApp() -> Bool {
        guard let url = URL(string: "whatsapp://") else { return false }
        return UIApplication.shared.canOpenURL(url)
    }

    func canOpenWhatsAppBusiness() -> Bool {
        guard let url = URL(string: "whatsapp-business://") else { return false }
        return UIApplication.shared.canOpenURL(url)
    }
}

#Preview {
    DocumentSuccessView(documentViewModel: DocumentViewModel())
}
