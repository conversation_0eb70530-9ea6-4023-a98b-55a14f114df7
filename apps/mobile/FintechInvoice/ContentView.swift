import SwiftUI

struct ContentView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if isLoading {
                LaunchScreenView()
            } else if authViewModel.isAuthenticated {
                MainTabView()
            } else {
                AuthenticationView()
            }
        }
        .onAppear {
            checkAuthenticationStatus()
        }
        .preferredColorScheme(.dark) // Default to dark theme
    }
    
    private func checkAuthenticationStatus() {
        Task {
            await authViewModel.checkAuthStatus()
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.5)) {
                    isLoading = false
                }
            }
        }
    }
}

struct LaunchScreenView: View {
    var body: some View {
        ZStack {
            Color.background
                .ignoresSafeArea()
            
            VStack(spacing: 24) {
                // App Logo
                Image(systemName: "doc.text.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.primary)
                
                Text("Fintech Invoice")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                ProgressView()
                    .scaleEffect(1.2)
                    .tint(.primary)
            }
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(AuthViewModel())
}
