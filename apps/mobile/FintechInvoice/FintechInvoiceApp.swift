import SwiftUI
import Supabase

@main
struct FintechInvoiceApp: App {
    @StateObject private var authViewModel = AuthViewModel()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authViewModel)
                .environment(\.layoutDirection, .rightToLeft)
                .onAppear {
                    print("🚀 App starting up...")
                    // Configure Supabase
                    SupabaseService.shared.configure()
                    print("✅ Supabase configuration completed")
                }
        }
    }
}
