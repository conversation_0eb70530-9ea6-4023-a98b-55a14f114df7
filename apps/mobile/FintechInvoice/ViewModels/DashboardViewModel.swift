import Foundation
import SwiftUI

@MainActor
class DashboardViewModel: ObservableObject {
    @Published var monthlyRevenue = "₪0"
    @Published var revenueChange = "+0%"
    @Published var vatLiability = "₪0"
    @Published var nextPaymentDate = "אין תשלום"
    @Published var openInvoicesCount = 0
    @Published var openInvoicesAmount = "₪0"
    @Published var pendingExpensesCount = 0
    @Published var recentDocuments: [DocumentSummary] = []
    @Published var isLoading = false
    
    private let supabaseService = SupabaseService.shared
    
    func loadDashboardData() {
        isLoading = true
        
        Task {
            await loadStats()
            await loadRecentDocuments()
            
            await MainActor.run {
                isLoading = false
            }
        }
    }
    
    private func loadStats() async {
        // TODO: Implement actual data loading from Supabase
        // For now, using mock data
        
        await MainActor.run {
            monthlyRevenue = "₪45,230"
            revenueChange = "+12.5%"
            vatLiability = "₪8,141"
            nextPaymentDate = "15/02/2025"
            openInvoicesCount = 7
            openInvoicesAmount = "₪23,450"
            pendingExpensesCount = 3
        }
    }
    
    private func loadRecentDocuments() async {
        // TODO: Implement actual data loading from Supabase
        // For now, using mock data
        
        let mockDocuments = [
            DocumentSummary(
                id: "1",
                documentNumber: "INV-2025-001",
                customerName: "חברת ABC בע״מ",
                amount: 5420.00,
                status: .sent,
                date: Date().addingTimeInterval(-86400)
            ),
            DocumentSummary(
                id: "2",
                documentNumber: "INV-2025-002",
                customerName: "XYZ טכנולוגיות",
                amount: 8750.00,
                status: .pendingAllocation,
                date: Date().addingTimeInterval(-172800)
            ),
            DocumentSummary(
                id: "3",
                documentNumber: "REC-2025-001",
                customerName: "חברת DEF",
                amount: 2100.00,
                status: .paid,
                date: Date().addingTimeInterval(-259200)
            )
        ]
        
        await MainActor.run {
            recentDocuments = mockDocuments
        }
    }
}

// MARK: - Data Models
struct DocumentSummary: Identifiable {
    let id: String
    let documentNumber: String
    let customerName: String
    let amount: Double
    let status: DocumentStatus
    let date: Date
    
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: amount)) ?? "₪\(amount)"
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.locale = Locale(identifier: "he_IL")
        return formatter.string(from: date)
    }
    
    var statusText: String {
        switch status {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין להקצאה"
        case .approved:
            return "מאושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var statusColor: Color {
        switch status {
        case .draft:
            return .mutedForeground
        case .pendingAllocation:
            return .warning
        case .approved:
            return .primary
        case .sent:
            return .primary
        case .paid:
            return .success
        case .cancelled:
            return .destructive
        }
    }
}
