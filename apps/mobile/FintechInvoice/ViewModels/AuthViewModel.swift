import Foundation
import SwiftUI
import Supabase

@MainActor
class AuthViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var userCompanies: [Company] = []
    @Published var selectedCompany: Company?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    private let supabaseService = SupabaseService.shared
    
    init() {
        // Listen for auth state changes
        Task {
            await checkAuthStatus()
        }
    }
    
    func checkAuthStatus() async {
        print("🔐 Checking auth status...")
        do {
            let user = try await supabaseService.getCurrentUser()
            currentUser = user
            isAuthenticated = user != nil

            if let user = user {
                print("✅ User authenticated: \(user.email ?? "no email")")
                await loadUserCompanies()
            } else {
                print("❌ No authenticated user found")
                userCompanies = []
                selectedCompany = nil
            }
        } catch {
            print("❌ Auth check failed: \(error)")
            isAuthenticated = false
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
        }
    }
    
    func signIn(email: String, password: String) async {
        isLoading = true
        errorMessage = nil

        do {
            let session = try await supabaseService.signIn(email: email, password: password)
            currentUser = session.user
            isAuthenticated = true
            await loadUserCompanies()
        } catch {
            let errorMsg: String
            if error.localizedDescription.contains("Invalid login credentials") {
                errorMsg = "שגיאה: אימייל או סיסמה שגויים"
            } else {
                errorMsg = "שגיאה בהתחברות: \(error.localizedDescription)"
            }
            errorMessage = errorMsg
        }

        isLoading = false
    }
    
    func signUp(registrationData: RegistrationData) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        do {
            // Use the auth-register edge function for complete registration
            let registerResponse = try await supabaseService.signUp(registrationData: registrationData)

            // Registration successful - user and company created via edge function
            print("✅ Registration completed via edge function")
            print("📧 User: \(registerResponse.user.email)")
            print("🏢 Company: \(registerResponse.company.name)")
            print("📬 Verification email sent: \(registerResponse.verification_email_sent)")

            // Don't auto sign-in - user needs to verify email first
            currentUser = nil
            isAuthenticated = false

            // Show success message with email status
            if registerResponse.verification_email_sent {
                successMessage = "נרשמת בהצלחה! אנא בדוק את האימייל שלך לאישור החשבון"
            } else {
                successMessage = "נרשמת בהצלחה! אך אירעה שגיאה בשליחת אימייל האימות. אנא נסה לבקש אימייל חדש"
            }

        } catch {
            let errorMsg = "שגיאה ברישום: \(error.localizedDescription)"
            errorMessage = errorMsg
            print("❌ Registration failed: \(error)")
        }

        isLoading = false
    }
    
    func signOut() async {
        isLoading = true
        
        do {
            try await supabaseService.signOut()
            isAuthenticated = false
            currentUser = nil
            userCompanies = []
            selectedCompany = nil
        } catch {
            errorMessage = "שגיאה ביציאה: \(error.localizedDescription)"

        }
        
        isLoading = false
    }
    
    private func loadUserCompanies() async {
        guard let user = currentUser else {
            print("❌ No current user when trying to load companies")
            return
        }

        print("🏢 Loading companies for user: \(user.id.uuidString)")
        do {
            let companiesData = try await supabaseService.getUserCompanies(userId: user.id.uuidString)
            print("📊 Raw companies data count: \(companiesData.count)")

            userCompanies = companiesData.compactMap { data in
                let companyData = data.companies
                guard let companyId = companyData.id else {
                    return nil
                }
                return Company(
                    id: companyId,
                    name: companyData.name,
                    nameEnglish: companyData.nameEnglish,
                    businessNumber: companyData.businessNumber,
                    subscriptionTier: companyData.subscriptionTier ?? "free"
                )
            }

            print("✅ Loaded \(userCompanies.count) companies for user")
            for company in userCompanies {
                print("🏢 Company: \(company.name) (ID: \(company.id))")
            }

            // Select first company by default
            if selectedCompany == nil && !userCompanies.isEmpty {
                selectedCompany = userCompanies.first
                print("✅ Selected default company: \(selectedCompany?.name ?? "nil") (ID: \(selectedCompany?.id ?? "nil"))")
            } else if userCompanies.isEmpty {
                print("⚠️ No companies found for user")
            }

        } catch {
            print("❌ Failed to load user companies: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            if let user = currentUser {
                print("❌ User ID: \(user.id.uuidString)")
            }
            // Clear companies on error
            userCompanies = []
            selectedCompany = nil
        }
    }
}

// MARK: - Data Models
// RegistrationData moved to SupabaseService.swift

struct Company: Identifiable, Codable {
    let id: String
    let name: String // Hebrew name as primary name
    let nameEnglish: String?
    let businessNumber: String
    let subscriptionTier: String
}
