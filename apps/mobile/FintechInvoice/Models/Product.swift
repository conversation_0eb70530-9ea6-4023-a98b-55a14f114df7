import Foundation

// MARK: - Product Model
struct Product: Identifiable, Codable {
    let id: String
    let companyId: String
    let sku: String?
    let nameHebrew: String
    let nameEnglish: String?
    let descriptionHebrew: String?
    let descriptionEnglish: String?
    let unitPrice: Double
    let currency: String
    let vatRate: Double
    let isService: Bool
    let isActive: Bool
    let createdAt: String
    let updatedAt: String
    
    // Computed properties for display
    var displayName: String {
        return nameHebrew
    }
    
    var formattedPrice: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: unitPrice)) ?? "₪\(unitPrice)"
    }
    
    var formattedVatRate: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .percent
        formatter.maximumFractionDigits = 1
        return formatter.string(from: NSNumber(value: vatRate / 100)) ?? "\(vatRate)%"
    }
    
    var typeDisplayName: String {
        return isService ? "שירות" : "מוצר"
    }
    
    var typeIcon: String {
        return isService ? "wrench.and.screwdriver" : "cube.box"
    }
    
    var fullDescription: String {
        if let description = descriptionHebrew, !description.isEmpty {
            return description
        }
        return displayName
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case sku
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case unitPrice = "unit_price"
        case currency
        case vatRate = "vat_rate"
        case isService = "is_service"
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Product Creation Request
struct CreateProductRequest: Codable {
    let companyId: String
    let sku: String?
    let nameHebrew: String
    let nameEnglish: String?
    let descriptionHebrew: String?
    let descriptionEnglish: String?
    let unitPrice: Double
    let currency: String
    let vatRate: Double
    let isService: Bool
    
    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case sku
        case nameHebrew = "name_hebrew"
        case nameEnglish = "name_english"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case unitPrice = "unit_price"
        case currency
        case vatRate = "vat_rate"
        case isService = "is_service"
    }
}

// MARK: - Product Response Types
struct ProductResponse: Codable {
    let success: Bool
    let data: ProductResponseData?
    let error: String?
}

struct ProductResponseData: Codable {
    let product: Product
}

struct ProductsListResponse: Codable {
    let success: Bool
    let data: ProductsListData?
    let error: String?
}

struct ProductsListData: Codable {
    let products: [Product]
    let total: Int?
}

// MARK: - Product Form Data (for UI)
struct ProductFormData {
    var nameHebrew: String = ""
    var descriptionHebrew: String = ""
    var unitPrice: String = ""
    var vatRate: Double = 18.0 // Default Israeli VAT rate
    
    var isValid: Bool {
        return !nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !unitPrice.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               unitPriceValue > 0
    }
    
    var unitPriceValue: Double {
        return Double(unitPrice.replacingOccurrences(of: ",", with: ".")) ?? 0.0
    }
    
    var nameError: String? {
        if nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "שם הפריט הוא שדה חובה"
        }
        return nil
    }
    
    var priceError: String? {
        if unitPrice.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "מחיר הוא שדה חובה"
        }
        if unitPriceValue <= 0 {
            return "מחיר חייב להיות גדול מאפס"
        }
        return nil
    }
    
    var vatRateError: String? {
        if vatRate < 0 || vatRate > 100 {
            return "אחוז מע״מ חייב להיות בין 0 ל-100"
        }
        return nil
    }
    
    func toCreateRequest(companyId: String) -> CreateProductRequest {
        return CreateProductRequest(
            companyId: companyId,
            sku: nil, // SKU field removed from UI
            nameHebrew: nameHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            nameEnglish: nil,
            descriptionHebrew: descriptionHebrew.isEmpty ? nil : descriptionHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            descriptionEnglish: nil,
            unitPrice: unitPriceValue,
            currency: "ILS",
            vatRate: vatRate,
            isService: false // Default to false since product/service selector removed
        )
    }
}

// MARK: - VAT Rate Presets
enum VATRate: Double, CaseIterable {
    case zero = 0.0
    case standard = 18.0

    var displayName: String {
        switch self {
        case .zero:
            return "פטור ממע״מ (0%)"
        case .standard:
            return "מע״מ רגיל (18%)"
        }
    }
    
    static var allDisplayNames: [String] {
        return VATRate.allCases.map { $0.displayName }
    }
    
    static func rate(for displayName: String) -> Double {
        return VATRate.allCases.first { $0.displayName == displayName }?.rawValue ?? 18.0
    }
}

// MARK: - Product Categories (for future use)
enum ProductCategory: String, CaseIterable {
    case consulting = "consulting"
    case software = "software"
    case hardware = "hardware"
    case training = "training"
    case support = "support"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .consulting:
            return "ייעוץ"
        case .software:
            return "תוכנה"
        case .hardware:
            return "חומרה"
        case .training:
            return "הדרכה"
        case .support:
            return "תמיכה"
        case .other:
            return "אחר"
        }
    }
}
