# iOS App Testing Guide

## Prerequisites

### Required Software
- **Xcode 15.0+**: Download from Mac App Store
- **iOS Simulator**: Included with Xcode
- **Physical iPhone**: For final testing (iOS 15.0+)

### Setup Steps
1. Install Xcode from Mac App Store
2. Open Terminal and run: `sudo xcode-select --switch /Applications/Xcode.app`
3. Accept Xcode license: `sudo xcodebuild -license accept`

## Opening the Project

### Method 1: Xcode GUI
1. Open Xcode
2. Select "Open a project or file"
3. Navigate to `apps/mobile/FintechInvoice.xcodeproj`
4. Click "Open"

### Method 2: Command Line
```bash
cd apps/mobile
open FintechInvoice.xcodeproj
```

## Initial Configuration

### 1. Set Development Team
- Select project in navigator
- Go to "Signing & Capabilities" tab
- Choose your Apple Developer account under "Team"
- Xcode will automatically generate provisioning profile

### 2. Configure Supabase (Already Done)
The Config.plist file has been updated with your Supabase credentials:
- URL: https://zhwqtgypoueykwgphmqn.supabase.co
- Anon Key: [Your key is configured]

### 3. Add Dependencies
The project is configured to use Swift Package Manager:
- Supabase Swift SDK will be automatically resolved
- No manual installation needed

## Running the App

### Simulator Testing
1. Select a simulator from the device menu (iPhone 14 Pro recommended)
2. Press Cmd+R or click the "Play" button
3. App should launch in simulator

### Device Testing
1. Connect iPhone via USB
2. Trust the computer on iPhone
3. Select your device from device menu
4. Press Cmd+R to build and run

## Testing Checklist

### ✅ Basic Functionality
- [ ] App launches without crashes
- [ ] Launch screen displays correctly
- [ ] Authentication screens load
- [ ] RTL layout works properly
- [ ] Hebrew text renders correctly

### ✅ Authentication Flow
- [ ] Login screen displays
- [ ] Registration flow works (6 steps)
- [ ] Form validation functions
- [ ] Supabase connection works
- [ ] Error messages display in Hebrew

### ✅ Dashboard Features
- [ ] Tab navigation works
- [ ] Dashboard loads with mock data
- [ ] Stats cards display correctly
- [ ] Quick actions are responsive
- [ ] Settings screen functions

### ✅ Design System
- [ ] Dark theme applied consistently
- [ ] Colors match web app
- [ ] Typography renders properly
- [ ] Spacing and layout correct
- [ ] Cosmic glass effects work

## Common Issues & Solutions

### Issue: "Developer cannot be verified"
**Solution**: Go to Settings > General > VPN & Device Management > Trust developer

### Issue: Build fails with Supabase errors
**Solution**: 
1. Clean build folder (Cmd+Shift+K)
2. Reset package caches: File > Packages > Reset Package Caches
3. Rebuild project

### Issue: RTL layout not working
**Solution**: 
1. Change iOS language to Hebrew in Settings
2. Restart simulator
3. Test app again

### Issue: Fonts not loading
**Solution**: 
- System fonts are used as fallbacks
- Custom fonts can be added later to Assets

## Performance Testing

### Memory Usage
- Monitor in Xcode's Debug Navigator
- Should stay under 100MB for basic usage

### Launch Time
- Target: Under 3 seconds on device
- Monitor in Instruments app

### Network Requests
- Test with poor network conditions
- Verify error handling works

## Debugging

### Console Logs
- View in Xcode's Debug Area
- Look for Supabase connection logs
- Check for authentication errors

### Breakpoints
- Set breakpoints in ViewModels
- Debug authentication flow
- Verify data flow

### Instruments
- Use for performance profiling
- Memory leak detection
- Network analysis

## Device Testing Matrix

### iPhone Models (Recommended)
- iPhone 12 (6.1")
- iPhone 13 Pro (6.1")
- iPhone 14 Pro Max (6.7")
- iPhone 15 (6.1")

### iOS Versions
- iOS 15.0 (minimum)
- iOS 16.0 (recommended)
- iOS 17.0 (latest)

## Automated Testing (Future)

### Unit Tests
```swift
// Example test structure
class AuthViewModelTests: XCTestCase {
    func testLoginValidation() {
        // Test login form validation
    }
    
    func testRegistrationFlow() {
        // Test multi-step registration
    }
}
```

### UI Tests
```swift
// Example UI test
class FintechInvoiceUITests: XCTestCase {
    func testAuthenticationFlow() {
        // Test complete auth flow
    }
}
```

## Deployment Testing

### TestFlight (Beta Testing)
1. Archive app in Xcode
2. Upload to App Store Connect
3. Create TestFlight build
4. Invite beta testers

### App Store Submission
1. Complete all testing
2. Generate final archive
3. Upload to App Store Connect
4. Submit for review

## Troubleshooting Commands

### Reset Simulator
```bash
xcrun simctl erase all
```

### Clean Derived Data
```bash
rm -rf ~/Library/Developer/Xcode/DerivedData
```

### Reset Package Caches
```bash
rm -rf ~/Library/Caches/org.swift.swiftpm
```

## Next Steps

1. **Complete Testing**: Run through all test scenarios
2. **Add Real Data**: Connect to actual Supabase backend
3. **Performance Optimization**: Profile and optimize
4. **App Store Preparation**: Create assets and descriptions
5. **Beta Testing**: Deploy to TestFlight for user feedback

## Support

If you encounter issues:
1. Check this testing guide
2. Review Xcode console logs
3. Verify Supabase configuration
4. Test on different devices/simulators
5. Clean and rebuild project
