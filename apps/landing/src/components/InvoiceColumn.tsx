import React, { useState } from 'react';
import InvoiceCard from './InvoiceCard';
import { MetricColumn, InvoiceItem } from './TaskBoard';

interface InvoiceColumnProps {
  column: MetricColumn;
  onDrop: (e: React.DragEvent, columnId: string) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onInvoiceDragStart: (e: React.DragEvent, invoice: InvoiceItem) => void;
  onInvoiceDragEnd: () => void;
  onStatusChange: (invoiceId: string, newStatus: string) => void;
}

const InvoiceColumn: React.FC<InvoiceColumnProps> = ({
  column,
  onDrop,
  onDragOver,
  onDragLeave,
  onInvoiceDragStart,
  onInvoiceDragEnd,
  onStatusChange
}) => {
  const [isOver, setIsOver] = useState(false);
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsOver(true);
    onDragOver(e);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    setIsOver(false);
    onDragLeave(e);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    setIsOver(false);
    onDrop(e, column.id);
  };

  // Format amount with commas
  const formatAmount = (amount: number) => {
    return amount.toLocaleString('he-IL');
  };

  // Get column status color
  const getColumnColor = (columnId: string) => {
    const colorMap: { [key: string]: string } = {
      'draft': 'bg-muted/30',
      'sent': 'bg-blue-500/30',
      'paid': 'bg-green-500/30',
      'overdue': 'bg-red-500/30'
    };
    return colorMap[columnId] || 'bg-muted/30';
  };

  const getColumnTextColor = (columnId: string) => {
    const colorMap: { [key: string]: string } = {
      'draft': 'text-muted-foreground',
      'sent': 'text-blue-400',
      'paid': 'text-green-400',
      'overdue': 'text-red-400'
    };
    return colorMap[columnId] || 'text-muted-foreground';
  };

  return (
    <div 
      className={`flex flex-col w-72 min-w-72 rounded-lg border border-border backdrop-blur-sm transition-all duration-300 ${
        isOver ? 'column-highlight border-muted/50' : 'bg-card/50'
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* Column Header */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className={`h-3 w-3 rounded-full ${getColumnColor(column.id)}`}></span>
            <h4 className="font-medium text-sm text-foreground">{column.title}</h4>
            <span className="text-xs bg-muted/50 px-2 py-0.5 rounded-full text-muted-foreground">
              {column.items.length}
            </span>
          </div>
          <div className="text-muted-foreground">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 12V12.01M8 12V12.01M16 12V12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
        
        {/* Total Amount */}
        <div className="flex items-center gap-1">
          <span className={`text-lg font-bold ${getColumnTextColor(column.id)}`}>
            {formatAmount(column.totalAmount)}
          </span>
          <span className="text-sm text-muted-foreground">₪</span>
        </div>
      </div>
      
      {/* Invoice Items */}
      <div className="p-2 flex-1 space-y-2 overflow-auto">
        {column.items.map((invoice) => (
          <InvoiceCard
            key={invoice.id}
            invoice={invoice}
            onDragStart={onInvoiceDragStart}
            onDragEnd={onInvoiceDragEnd}
            onStatusChange={onStatusChange}
          />
        ))}
      </div>
    </div>
  );
};

export default InvoiceColumn;
