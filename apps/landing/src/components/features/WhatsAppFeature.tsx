import React, { useState, useEffect } from 'react';

const WhatsAppFeature: React.FC = () => {
  const [currentMessage, setCurrentMessage] = useState(0);
  const [showResponse, setShowResponse] = useState(false);

  const messages = [
    {
      type: 'user',
      content: '🎤 צור חשבונית לחברת טכנולוגיה בע"מ על פיתוח אתר - 15,000 ש"ח',
      time: '14:32'
    },
    {
      type: 'bot',
      content: '✅ חשבונית נוצרה בהצלחה!\n📄 חשבונית #1005\n💰 15,000 ש"ח\n🏢 חברת טכנולוגיה בע"מ\n📅 תאריך יעד: 30 יוני',
      time: '14:32'
    },
    {
      type: 'user',
      content: '📸 [תמונה של קבלה]',
      time: '14:35',
      isImage: true
    },
    {
      type: 'bot',
      content: '📊 הוצאה חדשה נוספה!\n🍽️ ארוחת עסקים - 280 ש"ח\n📍 מסעדת השף\n📅 15 יוני 2024',
      time: '14:35'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      if (currentMessage < messages.length - 1) {
        setCurrentMessage(prev => prev + 1);
        setShowResponse(false);
        setTimeout(() => setShowResponse(true), 1000);
      } else {
        setCurrentMessage(0);
        setShowResponse(false);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [currentMessage, messages.length]);

  return (
    <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
      {/* Content */}
      <div className="flex-1 text-center lg:text-right">
        <div className="inline-flex items-center gap-2 bg-muted/20 text-muted-foreground px-3 py-1 rounded-full text-sm font-medium mb-4">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.60556 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          עוזר AI ייחודי
        </div>
        
        <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          עוזר WhatsApp חכם
        </h3>
        
        <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
          קבלו עוזר אישי ב-WhatsApp שמבין עברית, מקליט הוצאות, יוצר חשבוניות ומנהל את העסק שלכם 
          באמצעות הודעות טקסט או קוליות פשוטות
        </p>

        <div className="space-y-4">
          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">יצירת חשבוניות מיידית</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M12 2V22M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6312 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6312 13.6815 18 14.5717 18 15.5C18 16.4283 17.6312 17.3185 16.9749 17.9749C16.3185 18.6312 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">זיהוי אוטומטי של הוצאות</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">סנכרון מיידי עם המערכת</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Animated Phone */}
      <div className="flex-shrink-0">
        <div className="relative">
          {/* Phone Frame */}
          <div className="w-80 h-[600px] bg-gradient-to-b from-gray-900 to-black rounded-[3rem] p-2 shadow-2xl">
            <div className="w-full h-full bg-black rounded-[2.5rem] overflow-hidden relative">
              {/* Status Bar */}
              <div className="flex justify-between items-center px-6 py-3 text-white text-sm">
                <span>9:41</span>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-2 border border-white rounded-sm">
                    <div className="w-3 h-1 bg-white rounded-sm m-0.5"></div>
                  </div>
                </div>
              </div>

              {/* WhatsApp Header */}
              <div className="bg-green-600 px-4 py-3 flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-green-400 flex items-center justify-center text-white font-bold">
                  AI
                </div>
                <div>
                  <h4 className="text-white font-medium">עוזר פיננסי AI</h4>
                  <p className="text-green-100 text-xs">פעיל עכשיו</p>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 bg-gray-100 p-4 space-y-4 h-[480px] overflow-hidden">
                {messages.slice(0, currentMessage + 1).map((message, index) => (
                  <div
                    key={index}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fadeIn`}
                  >
                    <div
                      className={`max-w-xs px-4 py-2 rounded-2xl ${
                        message.type === 'user'
                          ? 'bg-green-500 text-white rounded-br-md'
                          : 'bg-white text-gray-800 rounded-bl-md shadow-sm'
                      }`}
                    >
                      {message.isImage ? (
                        <div className="space-y-2">
                          <div className="w-32 h-24 bg-gray-300 rounded-lg flex items-center justify-center">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-500">
                              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
                              <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
                              <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                          </div>
                          <p className="text-sm">קבלה מהמסעדה</p>
                        </div>
                      ) : (
                        <p className="text-sm whitespace-pre-line">{message.content}</p>
                      )}
                      <p className={`text-xs mt-1 ${message.type === 'user' ? 'text-green-100' : 'text-gray-500'}`}>
                        {message.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Input Area */}
              <div className="bg-gray-200 p-4 flex items-center gap-2">
                <div className="flex-1 bg-white rounded-full px-4 py-2 text-gray-500 text-sm">
                  הקלד הודעה...
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Floating Elements */}
          <div className="absolute -top-4 -left-4 w-12 h-12 bg-foreground rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-background">
              <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.60556 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppFeature;
