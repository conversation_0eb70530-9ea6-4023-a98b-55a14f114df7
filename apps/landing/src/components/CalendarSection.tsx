import React, { useEffect } from 'react';

const CalendarSection = () => {
  useEffect(() => {
    // Clear any existing calendar
    const existingCalendar = document.getElementById('my-cal-inline-pay-kwik');
    if (existingCalendar) {
      existingCalendar.innerHTML = '';
    }

    // Load Cal.com script and initialize calendar
    const loadCalendar = () => {
      // Cal.com embed script
      (function (C: any, A: string, L: string) {
        let p = function (a: any, ar: any) { a.q.push(ar); };
        let d = C.document;
        C.Cal = C.Cal || function () {
          let cal = C.Cal;
          let ar = arguments;
          if (!cal.loaded) {
            cal.ns = {};
            cal.q = cal.q || [];
            d.head.appendChild(d.createElement("script")).src = A;
            cal.loaded = true;
          }
          if (ar[0] === L) {
            const api = function () { p(api, arguments); };
            const namespace = ar[1];
            api.q = api.q || [];
            if (typeof namespace === "string") {
              cal.ns[namespace] = cal.ns[namespace] || api;
              p(cal.ns[namespace], ar);
              p(cal, ["initNamespace", namespace]);
            } else p(cal, ar);
            return;
          }
          p(cal, ar);
        };
      })(window, "https://app.cal.com/embed/embed.js", "init");

      // Initialize Cal
      (window as any).Cal("init", "pay-kwik", { origin: "https://app.cal.com" });

      // Configure inline calendar
      (window as any).Cal.ns["pay-kwik"]("inline", {
        elementOrSelector: "#my-cal-inline-pay-kwik",
        config: { "layout": "month_view" },
        calLink: "simon-kwik/pay-kwik",
      });

      // Set UI theme
      (window as any).Cal.ns["pay-kwik"]("ui", {
        "hideEventTypeDetails": false,
        "layout": "month_view"
      });
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(loadCalendar, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <section id="calendar" className="w-full py-20 px-6 md:px-12 bg-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto space-y-16 relative z-10">
        <div className="text-center space-y-4 max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium tracking-tighter text-foreground">
            קבע פגישה
          </h2>
          <p className="text-muted-foreground text-lg">
            בחר זמן נוח עבורך לפגישת ייעוץ אישית
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="rounded-xl border border-border bg-card/50 backdrop-blur-sm p-6">
            <div 
              style={{ width: '100%', height: '600px', overflow: 'scroll' }} 
              id="my-cal-inline-pay-kwik"
              className="rounded-lg"
            >
              {/* Calendar will be loaded here */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CalendarSection;
