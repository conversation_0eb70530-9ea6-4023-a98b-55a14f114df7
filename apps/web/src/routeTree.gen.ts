/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as RegisterRouteImport } from './routes/register'
import { Route as LoginRouteImport } from './routes/login'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as DashboardSettingsRouteImport } from './routes/dashboard/settings'
import { Route as DashboardReportsRouteImport } from './routes/dashboard/reports'
import { Route as DashboardProductsRouteImport } from './routes/dashboard/products'
import { Route as DashboardExpensesRouteImport } from './routes/dashboard/expenses'
import { Route as DashboardCustomersRouteImport } from './routes/dashboard/customers'
import { Route as DashboardDocumentsCreateRouteImport } from './routes/dashboard/documents/create'

const RegisterRoute = RegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardSettingsRoute = DashboardSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardReportsRoute = DashboardReportsRouteImport.update({
  id: '/reports',
  path: '/reports',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardProductsRoute = DashboardProductsRouteImport.update({
  id: '/products',
  path: '/products',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardExpensesRoute = DashboardExpensesRouteImport.update({
  id: '/expenses',
  path: '/expenses',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardCustomersRoute = DashboardCustomersRouteImport.update({
  id: '/customers',
  path: '/customers',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardDocumentsCreateRoute =
  DashboardDocumentsCreateRouteImport.update({
    id: '/documents/create',
    path: '/documents/create',
    getParentRoute: () => DashboardRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/dashboard/customers': typeof DashboardCustomersRoute
  '/dashboard/expenses': typeof DashboardExpensesRoute
  '/dashboard/products': typeof DashboardProductsRoute
  '/dashboard/reports': typeof DashboardReportsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/documents/create': typeof DashboardDocumentsCreateRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/dashboard/customers': typeof DashboardCustomersRoute
  '/dashboard/expenses': typeof DashboardExpensesRoute
  '/dashboard/products': typeof DashboardProductsRoute
  '/dashboard/reports': typeof DashboardReportsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard': typeof DashboardIndexRoute
  '/dashboard/documents/create': typeof DashboardDocumentsCreateRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/dashboard/customers': typeof DashboardCustomersRoute
  '/dashboard/expenses': typeof DashboardExpensesRoute
  '/dashboard/products': typeof DashboardProductsRoute
  '/dashboard/reports': typeof DashboardReportsRoute
  '/dashboard/settings': typeof DashboardSettingsRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/documents/create': typeof DashboardDocumentsCreateRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/login'
    | '/register'
    | '/dashboard/customers'
    | '/dashboard/expenses'
    | '/dashboard/products'
    | '/dashboard/reports'
    | '/dashboard/settings'
    | '/dashboard/'
    | '/dashboard/documents/create'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/register'
    | '/dashboard/customers'
    | '/dashboard/expenses'
    | '/dashboard/products'
    | '/dashboard/reports'
    | '/dashboard/settings'
    | '/dashboard'
    | '/dashboard/documents/create'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/login'
    | '/register'
    | '/dashboard/customers'
    | '/dashboard/expenses'
    | '/dashboard/products'
    | '/dashboard/reports'
    | '/dashboard/settings'
    | '/dashboard/'
    | '/dashboard/documents/create'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRouteWithChildren
  LoginRoute: typeof LoginRoute
  RegisterRoute: typeof RegisterRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/settings': {
      id: '/dashboard/settings'
      path: '/settings'
      fullPath: '/dashboard/settings'
      preLoaderRoute: typeof DashboardSettingsRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/reports': {
      id: '/dashboard/reports'
      path: '/reports'
      fullPath: '/dashboard/reports'
      preLoaderRoute: typeof DashboardReportsRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/products': {
      id: '/dashboard/products'
      path: '/products'
      fullPath: '/dashboard/products'
      preLoaderRoute: typeof DashboardProductsRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/expenses': {
      id: '/dashboard/expenses'
      path: '/expenses'
      fullPath: '/dashboard/expenses'
      preLoaderRoute: typeof DashboardExpensesRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/customers': {
      id: '/dashboard/customers'
      path: '/customers'
      fullPath: '/dashboard/customers'
      preLoaderRoute: typeof DashboardCustomersRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/dashboard/documents/create': {
      id: '/dashboard/documents/create'
      path: '/documents/create'
      fullPath: '/dashboard/documents/create'
      preLoaderRoute: typeof DashboardDocumentsCreateRouteImport
      parentRoute: typeof DashboardRoute
    }
  }
}

interface DashboardRouteChildren {
  DashboardCustomersRoute: typeof DashboardCustomersRoute
  DashboardExpensesRoute: typeof DashboardExpensesRoute
  DashboardProductsRoute: typeof DashboardProductsRoute
  DashboardReportsRoute: typeof DashboardReportsRoute
  DashboardSettingsRoute: typeof DashboardSettingsRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardDocumentsCreateRoute: typeof DashboardDocumentsCreateRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardCustomersRoute: DashboardCustomersRoute,
  DashboardExpensesRoute: DashboardExpensesRoute,
  DashboardProductsRoute: DashboardProductsRoute,
  DashboardReportsRoute: DashboardReportsRoute,
  DashboardSettingsRoute: DashboardSettingsRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardDocumentsCreateRoute: DashboardDocumentsCreateRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRouteWithChildren,
  LoginRoute: LoginRoute,
  RegisterRoute: RegisterRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
