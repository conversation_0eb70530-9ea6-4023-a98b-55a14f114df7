import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Badge } from '@fintech/ui'
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Package,
  DollarSign,
  Tag,
  ToggleLeft,
  ToggleRight
} from 'lucide-react'

interface Product {
  id: string
  sku?: string
  nameHebrew: string
  nameEnglish?: string
  descriptionHebrew?: string
  descriptionEnglish?: string
  unitPrice: number
  currency: string
  vatRate: number
  isService: boolean
  isActive: boolean
  timesUsed: number
  lastUsed?: string
}

function ProductsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [filterType, setFilterType] = useState<'all' | 'products' | 'services'>('all')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')

  // Mock products data
  const products: Product[] = [
    {
      id: '1',
      sku: 'CONS-001',
      nameHebrew: 'ייעוץ עסקי',
      nameEnglish: 'Business Consulting',
      descriptionHebrew: 'שירותי ייעוץ עסקי מקצועיים',
      descriptionEnglish: 'Professional business consulting services',
      unitPrice: 500,
      currency: 'ILS',
      vatRate: 18,
      isService: true,
      isActive: true,
      timesUsed: 25,
      lastUsed: '2025-01-20'
    },
    {
      id: '2',
      sku: 'SOFT-001',
      nameHebrew: 'רישיון תוכנה שנתי',
      nameEnglish: 'Annual Software License',
      descriptionHebrew: 'רישיון שימוש שנתי במערכת',
      unitPrice: 1200,
      currency: 'ILS',
      vatRate: 18,
      isService: false,
      isActive: true,
      timesUsed: 12,
      lastUsed: '2025-01-18'
    },
    {
      id: '3',
      sku: 'TRAIN-001',
      nameHebrew: 'הדרכה אישית',
      nameEnglish: 'Personal Training',
      descriptionHebrew: 'הדרכה אישית למערכת',
      unitPrice: 300,
      currency: 'ILS',
      vatRate: 18,
      isService: true,
      isActive: true,
      timesUsed: 8,
      lastUsed: '2025-01-15'
    },
    {
      id: '4',
      sku: 'EQUIP-001',
      nameHebrew: 'ציוד משרדי',
      nameEnglish: 'Office Equipment',
      descriptionHebrew: 'ציוד משרדי כללי',
      unitPrice: 150,
      currency: 'ILS',
      vatRate: 18,
      isService: false,
      isActive: false,
      timesUsed: 3,
      lastUsed: '2024-12-10'
    }
  ]

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.nameHebrew.includes(searchQuery) ||
                         (product.nameEnglish && product.nameEnglish.toLowerCase().includes(searchQuery.toLowerCase())) ||
                         (product.sku && product.sku.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesType = filterType === 'all' || 
                       (filterType === 'services' && product.isService) ||
                       (filterType === 'products' && !product.isService)
    
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'active' && product.isActive) ||
                         (filterStatus === 'inactive' && !product.isActive)
    
    return matchesSearch && matchesType && matchesStatus
  })

  const handleAddProduct = () => {
    setShowAddModal(true)
  }

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setShowEditModal(true)
  }

  const handleDeleteProduct = (productId: string) => {
    if (confirm('האם אתה בטוח שברצונך למחוק את המוצר?')) {
      console.log('Delete product:', productId)
      // TODO: Implement delete logic
    }
  }

  const handleToggleStatus = (product: Product) => {
    console.log('Toggle product status:', product.id, !product.isActive)
    // TODO: Implement status toggle logic
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">ניהול מוצרים ושירותים</h1>
            <p className="text-muted-foreground">נהל את קטלוג המוצרים והשירותים שלך</p>
          </div>
          
          <Button onClick={handleAddProduct}>
            <Plus className="ml-2 h-4 w-4" />
            הוסף מוצר/שירות
          </Button>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">חיפוש</Label>
                <div className="relative">
                  <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="חפש לפי שם או SKU"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10 text-right"
                    dir="rtl"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="type">סוג</Label>
                <select
                  id="type"
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as any)}
                  className="w-full p-2 border border-input bg-background rounded-md text-right"
                  dir="rtl"
                >
                  <option value="all">הכל</option>
                  <option value="products">מוצרים</option>
                  <option value="services">שירותים</option>
                </select>
              </div>
              
              <div>
                <Label htmlFor="status">סטטוס</Label>
                <select
                  id="status"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                  className="w-full p-2 border border-input bg-background rounded-md text-right"
                  dir="rtl"
                >
                  <option value="all">הכל</option>
                  <option value="active">פעיל</option>
                  <option value="inactive">לא פעיל</option>
                </select>
              </div>
              
              <div className="flex items-end">
                <Button variant="outline" className="w-full">
                  נקה מסננים
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Products List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>רשימת מוצרים ושירותים</CardTitle>
                <CardDescription>
                  {filteredProducts.length} פריטים נמצאו
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-0">
                  {filteredProducts.map((product) => (
                    <div
                      key={product.id}
                      className={`p-4 border-b cursor-pointer hover:bg-accent transition-colors ${
                        selectedProduct?.id === product.id ? 'bg-accent' : ''
                      }`}
                      onClick={() => setSelectedProduct(product)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 space-x-reverse mb-2">
                            <h3 className="font-medium">{product.nameHebrew}</h3>
                            {product.nameEnglish && (
                              <span className="text-sm text-muted-foreground">
                                ({product.nameEnglish})
                              </span>
                            )}
                            <Badge variant={product.isService ? "secondary" : "outline"}>
                              {product.isService ? 'שירות' : 'מוצר'}
                            </Badge>
                            <Badge variant={product.isActive ? "default" : "secondary"}>
                              {product.isActive ? 'פעיל' : 'לא פעיל'}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                            {product.sku && (
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <Tag className="h-3 w-3" />
                                <span>SKU: {product.sku}</span>
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <DollarSign className="h-3 w-3" />
                              <span>₪{product.unitPrice} (+{product.vatRate}% מע"מ)</span>
                            </div>
                            
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <Package className="h-3 w-3" />
                              <span>נמכר {product.timesUsed} פעמים</span>
                            </div>
                            
                            {product.lastUsed && (
                              <div className="text-xs">
                                שימוש אחרון: {new Date(product.lastUsed).toLocaleDateString('he-IL')}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-left">
                          <p className="font-medium text-lg">₪{product.unitPrice}</p>
                          <p className="text-sm text-muted-foreground">
                            +₪{(product.unitPrice * product.vatRate / 100).toFixed(2)} מע"מ
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Details */}
          <div className="lg:col-span-1">
            {selectedProduct ? (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{selectedProduct.nameHebrew}</CardTitle>
                        {selectedProduct.nameEnglish && (
                          <CardDescription>{selectedProduct.nameEnglish}</CardDescription>
                        )}
                      </div>
                      <div className="flex space-x-1 space-x-reverse">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditProduct(selectedProduct)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleToggleStatus(selectedProduct)}
                        >
                          {selectedProduct.isActive ? (
                            <ToggleRight className="h-4 w-4 text-green-600" />
                          ) : (
                            <ToggleLeft className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteProduct(selectedProduct.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">פרטי בסיס</h4>
                      <div className="space-y-2 text-sm">
                        {selectedProduct.sku && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">SKU:</span>
                            <span>{selectedProduct.sku}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">סוג:</span>
                          <span>{selectedProduct.isService ? 'שירות' : 'מוצר'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">סטטוס:</span>
                          <Badge variant={selectedProduct.isActive ? "default" : "secondary"}>
                            {selectedProduct.isActive ? 'פעיל' : 'לא פעיל'}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">תמחור</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">מחיר יחידה:</span>
                          <span className="font-medium">₪{selectedProduct.unitPrice}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">שיעור מע"מ:</span>
                          <span>{selectedProduct.vatRate}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">מע"מ:</span>
                          <span>₪{(selectedProduct.unitPrice * selectedProduct.vatRate / 100).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-medium border-t pt-2">
                          <span>מחיר כולל מע"מ:</span>
                          <span>₪{(selectedProduct.unitPrice * (1 + selectedProduct.vatRate / 100)).toFixed(2)}</span>
                        </div>
                      </div>
                    </div>

                    {(selectedProduct.descriptionHebrew || selectedProduct.descriptionEnglish) && (
                      <div>
                        <h4 className="font-medium mb-2">תיאור</h4>
                        <div className="space-y-2 text-sm">
                          {selectedProduct.descriptionHebrew && (
                            <p className="text-right" dir="rtl">{selectedProduct.descriptionHebrew}</p>
                          )}
                          {selectedProduct.descriptionEnglish && (
                            <p className="text-left text-muted-foreground" dir="ltr">
                              {selectedProduct.descriptionEnglish}
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="font-medium mb-2">סטטיסטיקות</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">נמכר:</span>
                          <span>{selectedProduct.timesUsed} פעמים</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">הכנסה כוללת:</span>
                          <span className="font-medium">
                            ₪{(selectedProduct.timesUsed * selectedProduct.unitPrice).toLocaleString()}
                          </span>
                        </div>
                        {selectedProduct.lastUsed && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">שימוש אחרון:</span>
                            <span>{new Date(selectedProduct.lastUsed).toLocaleDateString('he-IL')}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">בחר מוצר או שירות מהרשימה לצפייה בפרטים</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Add Product Modal Placeholder */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-2xl m-4">
              <CardHeader>
                <CardTitle>הוסף מוצר/שירות חדש</CardTitle>
                <CardDescription>הכנס את פרטי המוצר או השירות החדש</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="nameHebrew">שם בעברית</Label>
                      <Input
                        id="nameHebrew"
                        className="text-right"
                        dir="rtl"
                        placeholder="שם המוצר/שירות בעברית"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="unitPrice">מחיר יחידה</Label>
                      <Input
                        id="unitPrice"
                        type="number"
                        step="0.01"
                        className="text-right"
                        dir="rtl"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2 space-x-reverse">
                    <Button variant="outline" onClick={() => setShowAddModal(false)}>
                      ביטול
                    </Button>
                    <Button onClick={() => setShowAddModal(false)}>
                      שמור
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/products')({
  component: ProductsPage,
})
