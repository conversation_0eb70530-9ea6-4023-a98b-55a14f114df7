import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Badge } from '@fintech/ui'
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  Building,
  MapPin,
  FileText
} from 'lucide-react'

interface Customer {
  id: string
  businessNumber: string
  nameHebrew: string
  nameEnglish?: string
  vatId?: string
  billingAddressHebrew: string
  cityHebrew: string
  contactName?: string
  contactEmail?: string
  contactPhone?: string
  notes?: string
  totalInvoices: number
  totalAmount: number
  lastInvoiceDate?: string
}

function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)

  // Mock customers data
  const customers: Customer[] = [
    {
      id: '1',
      businessNumber: '*********',
      nameHebrew: 'חברת דוגמה בע"מ',
      nameEnglish: 'Example Company Ltd',
      vatId: '*********',
      billingAddressHebrew: 'רחוב הרצל 123, תל אביב',
      cityHebrew: 'תל אביב',
      contactName: 'יוסי כהן',
      contactEmail: '<EMAIL>',
      contactPhone: '050-1234567',
      notes: 'לקוח VIP - תנאי תשלום מיוחדים',
      totalInvoices: 15,
      totalAmount: 45000,
      lastInvoiceDate: '2025-01-20'
    },
    {
      id: '2',
      businessNumber: '*********',
      nameHebrew: 'טכנולוגיות חדשות בע"מ',
      nameEnglish: 'New Technologies Ltd',
      vatId: '*********',
      billingAddressHebrew: 'שדרות רוטשילד 45, תל אביב',
      cityHebrew: 'תל אביב',
      contactName: 'שרה לוי',
      contactEmail: '<EMAIL>',
      contactPhone: '052-9876543',
      totalInvoices: 8,
      totalAmount: 23000,
      lastInvoiceDate: '2025-01-18'
    },
    {
      id: '3',
      businessNumber: '*********',
      nameHebrew: 'שירותי ייעוץ מקצועי',
      billingAddressHebrew: 'רחוב בן יהודה 67, ירושלים',
      cityHebrew: 'ירושלים',
      contactName: 'דוד אברהם',
      contactEmail: '<EMAIL>',
      contactPhone: '054-5556677',
      totalInvoices: 3,
      totalAmount: 8500,
      lastInvoiceDate: '2025-01-15'
    }
  ]

  const filteredCustomers = customers.filter(customer =>
    customer.nameHebrew.includes(searchQuery) ||
    customer.businessNumber.includes(searchQuery) ||
    (customer.nameEnglish && customer.nameEnglish.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (customer.contactName && customer.contactName.includes(searchQuery))
  )

  const handleAddCustomer = () => {
    setShowAddModal(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer)
    setShowEditModal(true)
  }

  const handleDeleteCustomer = (customerId: string) => {
    if (confirm('האם אתה בטוח שברצונך למחוק את הלקוח?')) {
      console.log('Delete customer:', customerId)
      // TODO: Implement delete logic
    }
  }

  const handleCreateInvoice = (customer: Customer) => {
    console.log('Create invoice for customer:', customer.id)
    // TODO: Navigate to invoice creation with pre-selected customer
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">ניהול לקוחות</h1>
            <p className="text-muted-foreground">נהל את פרטי הלקוחות שלך</p>
          </div>
          
          <Button onClick={handleAddCustomer}>
            <Plus className="ml-2 h-4 w-4" />
            הוסף לקוח חדש
          </Button>
        </div>

        {/* Search Bar */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="חפש לפי שם, מספר עסק או איש קשר"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 text-right"
                dir="rtl"
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Customers List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>רשימת לקוחות</CardTitle>
                <CardDescription>
                  {filteredCustomers.length} לקוחות נמצאו
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-0">
                  {filteredCustomers.map((customer) => (
                    <div
                      key={customer.id}
                      className={`p-4 border-b cursor-pointer hover:bg-accent transition-colors ${
                        selectedCustomer?.id === customer.id ? 'bg-accent' : ''
                      }`}
                      onClick={() => setSelectedCustomer(customer)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 space-x-reverse mb-2">
                            <h3 className="font-medium">{customer.nameHebrew}</h3>
                            {customer.nameEnglish && (
                              <span className="text-sm text-muted-foreground">
                                ({customer.nameEnglish})
                              </span>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <Building className="h-3 w-3" />
                              <span>ח.פ: {customer.businessNumber}</span>
                            </div>
                            
                            {customer.contactName && (
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <span>{customer.contactName}</span>
                              </div>
                            )}
                            
                            <div className="flex items-center space-x-1 space-x-reverse">
                              <MapPin className="h-3 w-3" />
                              <span>{customer.cityHebrew}</span>
                            </div>
                            
                            {customer.contactEmail && (
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <Mail className="h-3 w-3" />
                                <span>{customer.contactEmail}</span>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-left">
                          <p className="font-medium">₪{customer.totalAmount.toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">
                            {customer.totalInvoices} חשבוניות
                          </p>
                          {customer.lastInvoiceDate && (
                            <p className="text-xs text-muted-foreground">
                              אחרונה: {new Date(customer.lastInvoiceDate).toLocaleDateString('he-IL')}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customer Details */}
          <div className="lg:col-span-1">
            {selectedCustomer ? (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{selectedCustomer.nameHebrew}</CardTitle>
                        {selectedCustomer.nameEnglish && (
                          <CardDescription>{selectedCustomer.nameEnglish}</CardDescription>
                        )}
                      </div>
                      <div className="flex space-x-1 space-x-reverse">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditCustomer(selectedCustomer)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteCustomer(selectedCustomer.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">פרטי עסק</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">מספר עסק:</span>
                          <span>{selectedCustomer.businessNumber}</span>
                        </div>
                        {selectedCustomer.vatId && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">עוסק מורשה:</span>
                            <span>{selectedCustomer.vatId}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">כתובת:</span>
                          <span className="text-right">{selectedCustomer.billingAddressHebrew}</span>
                        </div>
                      </div>
                    </div>

                    {(selectedCustomer.contactName || selectedCustomer.contactEmail || selectedCustomer.contactPhone) && (
                      <div>
                        <h4 className="font-medium mb-2">פרטי קשר</h4>
                        <div className="space-y-2 text-sm">
                          {selectedCustomer.contactName && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">איש קשר:</span>
                              <span>{selectedCustomer.contactName}</span>
                            </div>
                          )}
                          {selectedCustomer.contactEmail && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">אימייל:</span>
                              <span>{selectedCustomer.contactEmail}</span>
                            </div>
                          )}
                          {selectedCustomer.contactPhone && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">טלפון:</span>
                              <span>{selectedCustomer.contactPhone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="font-medium mb-2">סטטיסטיקות</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">סה"כ חשבוניות:</span>
                          <span>{selectedCustomer.totalInvoices}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">סה"כ סכום:</span>
                          <span className="font-medium">₪{selectedCustomer.totalAmount.toLocaleString()}</span>
                        </div>
                        {selectedCustomer.lastInvoiceDate && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">חשבונית אחרונה:</span>
                            <span>{new Date(selectedCustomer.lastInvoiceDate).toLocaleDateString('he-IL')}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {selectedCustomer.notes && (
                      <div>
                        <h4 className="font-medium mb-2">הערות</h4>
                        <p className="text-sm text-muted-foreground">{selectedCustomer.notes}</p>
                      </div>
                    )}

                    <div className="pt-4 space-y-2">
                      <Button 
                        className="w-full"
                        onClick={() => handleCreateInvoice(selectedCustomer)}
                      >
                        <FileText className="ml-2 h-4 w-4" />
                        צור חשבונית חדשה
                      </Button>
                      
                      <div className="grid grid-cols-2 gap-2">
                        {selectedCustomer.contactEmail && (
                          <Button variant="outline" size="sm">
                            <Mail className="ml-1 h-3 w-3" />
                            שלח אימייל
                          </Button>
                        )}
                        {selectedCustomer.contactPhone && (
                          <Button variant="outline" size="sm">
                            <Phone className="ml-1 h-3 w-3" />
                            התקשר
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">בחר לקוח מהרשימה לצפייה בפרטים</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Add Customer Modal Placeholder */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="w-full max-w-2xl m-4">
              <CardHeader>
                <CardTitle>הוסף לקוח חדש</CardTitle>
                <CardDescription>הכנס את פרטי הלקוח החדש</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="nameHebrew">שם בעברית</Label>
                      <Input
                        id="nameHebrew"
                        className="text-right"
                        dir="rtl"
                        placeholder="שם החברה בעברית"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="businessNumber">מספר עסק</Label>
                      <Input
                        id="businessNumber"
                        className="text-right"
                        dir="rtl"
                        placeholder="*********"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2 space-x-reverse">
                    <Button variant="outline" onClick={() => setShowAddModal(false)}>
                      ביטול
                    </Button>
                    <Button onClick={() => setShowAddModal(false)}>
                      שמור
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/customers')({
  component: CustomersPage,
})
