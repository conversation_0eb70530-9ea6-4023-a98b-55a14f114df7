import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Badge } from '@fintech/ui'
import { 
  Filter, 
  Search, 
  Upload, 
  Mail, 
  AlertTriangle, 
  Check, 
  X,
  Calendar,
  DollarSign
} from 'lucide-react'

interface Expense {
  id: string
  vendorName: string
  amount: number
  vatAmount: number
  totalAmount: number
  date: string
  category: string
  status: 'pending' | 'approved' | 'rejected'
  duplicateRisk: 'none' | 'low' | 'high'
  source: 'manual' | 'email_scan' | 'upload'
  documentUrl?: string
  extractedData?: any
}

function ExpensesPage() {
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [dateRange, setDateRange] = useState({ start: '', end: '' })

  // Mock expenses data
  const expenses: Expense[] = [
    {
      id: '1',
      vendorName: 'חברת חשמל',
      amount: 850,
      vatAmount: 153,
      totalAmount: 1003,
      date: '2025-01-15',
      category: 'utilities',
      status: 'pending',
      duplicateRisk: 'none',
      source: 'email_scan',
      documentUrl: '/documents/expense1.pdf'
    },
    {
      id: '2',
      vendorName: 'משרד עורכי דין כהן ושות',
      amount: 5000,
      vatAmount: 900,
      totalAmount: 5900,
      date: '2025-01-14',
      category: 'professional_services',
      status: 'pending',
      duplicateRisk: 'high',
      source: 'email_scan',
      documentUrl: '/documents/expense2.pdf'
    },
    {
      id: '3',
      vendorName: 'ציוד משרדי בע"מ',
      amount: 320,
      vatAmount: 57.6,
      totalAmount: 377.6,
      date: '2025-01-13',
      category: 'office_supplies',
      status: 'approved',
      duplicateRisk: 'none',
      source: 'manual'
    }
  ]

  const filteredExpenses = expenses.filter(expense => {
    const matchesStatus = statusFilter === 'all' || expense.status === statusFilter
    const matchesSearch = expense.vendorName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesDateRange = (!dateRange.start || expense.date >= dateRange.start) &&
                            (!dateRange.end || expense.date <= dateRange.end)
    
    return matchesStatus && matchesSearch && matchesDateRange
  })

  const handleApprove = (expenseId: string) => {
    console.log('Approve expense:', expenseId)
    // TODO: Implement approval logic
  }

  const handleReject = (expenseId: string) => {
    console.log('Reject expense:', expenseId)
    // TODO: Implement rejection logic
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">ממתין לאישור</Badge>
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">אושר</Badge>
      case 'rejected':
        return <Badge variant="destructive">נדחה</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getDuplicateRiskBadge = (risk: string) => {
    switch (risk) {
      case 'high':
        return <Badge variant="destructive" className="text-xs">סיכון כפילות גבוה</Badge>
      case 'low':
        return <Badge variant="secondary" className="text-xs">סיכון כפילות נמוך</Badge>
      default:
        return null
    }
  }

  const getCategoryName = (category: string) => {
    const categories: Record<string, string> = {
      'office_supplies': 'ציוד משרדי',
      'travel': 'נסיעות',
      'utilities': 'שירותים',
      'rent': 'שכירות',
      'professional_services': 'שירותים מקצועיים',
      'marketing': 'שיווק',
      'equipment': 'ציוד',
      'other': 'אחר'
    }
    return categories[category] || category
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">ניהול הוצאות</h1>
            <p className="text-muted-foreground">נהל ואשר הוצאות שנסרקו אוטומטית</p>
          </div>
          
          <div className="flex space-x-2 space-x-reverse">
            <Button variant="outline">
              <Mail className="ml-2 h-4 w-4" />
              חבר אימייל
            </Button>
            <Button>
              <Upload className="ml-2 h-4 w-4" />
              העלה מסמך
            </Button>
          </div>
        </div>

        {/* Filter Bar */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">חיפוש</Label>
                <div className="relative">
                  <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="חפש לפי שם ספק"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10 text-right"
                    dir="rtl"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="status">סטטוס</Label>
                <select
                  id="status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full p-2 border border-input bg-background rounded-md text-right"
                  dir="rtl"
                >
                  <option value="all">הכל</option>
                  <option value="pending">ממתין</option>
                  <option value="approved">אושר</option>
                  <option value="rejected">נדחה</option>
                </select>
              </div>
              
              <div>
                <Label htmlFor="dateStart">מתאריך</Label>
                <Input
                  id="dateStart"
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange({...dateRange, start: e.target.value})}
                />
              </div>
              
              <div>
                <Label htmlFor="dateEnd">עד תאריך</Label>
                <Input
                  id="dateEnd"
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange({...dateRange, end: e.target.value})}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Expenses List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>רשימת הוצאות</CardTitle>
                <CardDescription>
                  {filteredExpenses.length} הוצאות נמצאו
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {filteredExpenses.map((expense) => (
                    <div
                      key={expense.id}
                      className={`p-4 border-b cursor-pointer hover:bg-accent transition-colors ${
                        selectedExpense?.id === expense.id ? 'bg-accent' : ''
                      }`}
                      onClick={() => setSelectedExpense(expense)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex-1">
                          <h3 className="font-medium text-sm">{expense.vendorName}</h3>
                          <p className="text-xs text-muted-foreground">
                            {new Date(expense.date).toLocaleDateString('he-IL')}
                          </p>
                        </div>
                        <div className="text-left">
                          <p className="font-medium text-sm">₪{expense.totalAmount}</p>
                          {getStatusBadge(expense.status)}
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">
                          {getCategoryName(expense.category)}
                        </span>
                        <div className="flex space-x-1 space-x-reverse">
                          {getDuplicateRiskBadge(expense.duplicateRisk)}
                          {expense.source === 'email_scan' && (
                            <Badge variant="outline" className="text-xs">
                              <Mail className="w-3 h-3 ml-1" />
                              סרוק
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Expense Details */}
          <div className="lg:col-span-2">
            {selectedExpense ? (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{selectedExpense.vendorName}</CardTitle>
                        <CardDescription>
                          {new Date(selectedExpense.date).toLocaleDateString('he-IL')} • 
                          {getCategoryName(selectedExpense.category)}
                        </CardDescription>
                      </div>
                      <div className="flex space-x-2 space-x-reverse">
                        {getStatusBadge(selectedExpense.status)}
                        {getDuplicateRiskBadge(selectedExpense.duplicateRisk)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="text-center p-4 bg-muted rounded-lg">
                        <DollarSign className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">סכום לפני מע"מ</p>
                        <p className="text-lg font-bold">₪{selectedExpense.amount}</p>
                      </div>
                      
                      <div className="text-center p-4 bg-muted rounded-lg">
                        <DollarSign className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">מע"מ</p>
                        <p className="text-lg font-bold">₪{selectedExpense.vatAmount}</p>
                      </div>
                      
                      <div className="text-center p-4 bg-primary/10 rounded-lg">
                        <DollarSign className="h-6 w-6 mx-auto mb-2 text-primary" />
                        <p className="text-sm text-muted-foreground">סה"כ</p>
                        <p className="text-lg font-bold text-primary">₪{selectedExpense.totalAmount}</p>
                      </div>
                    </div>

                    {selectedExpense.status === 'pending' && (
                      <div className="flex space-x-2 space-x-reverse">
                        <Button 
                          onClick={() => handleApprove(selectedExpense.id)}
                          className="flex-1"
                        >
                          <Check className="ml-2 h-4 w-4" />
                          אשר הוצאה
                        </Button>
                        <Button 
                          variant="destructive"
                          onClick={() => handleReject(selectedExpense.id)}
                          className="flex-1"
                        >
                          <X className="ml-2 h-4 w-4" />
                          דחה הוצאה
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Document Preview */}
                {selectedExpense.documentUrl && (
                  <Card>
                    <CardHeader>
                      <CardTitle>מסמך מקורי</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-muted p-8 rounded-lg text-center">
                        <p className="text-muted-foreground mb-4">תצוגה מקדימה של המסמך</p>
                        <Button variant="outline">
                          צפה במסמך המלא
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Extracted Data */}
                {selectedExpense.extractedData && (
                  <Card>
                    <CardHeader>
                      <CardTitle>נתונים שחולצו</CardTitle>
                      <CardDescription>
                        נתונים שחולצו אוטומטית מהמסמך באמצעות AI
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>שם ספק</Label>
                            <Input 
                              value={selectedExpense.vendorName}
                              className="text-right"
                              dir="rtl"
                              readOnly
                            />
                          </div>
                          
                          <div>
                            <Label>תאריך</Label>
                            <Input 
                              type="date"
                              value={selectedExpense.date}
                              readOnly
                            />
                          </div>
                          
                          <div>
                            <Label>סכום</Label>
                            <Input 
                              value={`₪${selectedExpense.amount}`}
                              className="text-right"
                              dir="rtl"
                              readOnly
                            />
                          </div>
                          
                          <div>
                            <Label>קטגוריה</Label>
                            <Input 
                              value={getCategoryName(selectedExpense.category)}
                              className="text-right"
                              dir="rtl"
                              readOnly
                            />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">בחר הוצאה מהרשימה לצפייה בפרטים</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/expenses')({
  component: ExpensesPage,
})
