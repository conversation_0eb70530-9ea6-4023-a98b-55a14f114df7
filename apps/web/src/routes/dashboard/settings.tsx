import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Badge } from '@fintech/ui'
import { 
  Building, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard,
  Bell,
  Shield,
  Download,
  Upload,
  Trash2,
  Plus,
  Settings as SettingsIcon
} from 'lucide-react'

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('company')
  const [isLoading, setIsLoading] = useState(false)

  // Mock company data
  const [companyData, setCompanyData] = useState({
    businessNumber: '*********',
    nameHebrew: 'חברת דוגמה בע"מ',
    nameEnglish: 'Example Company Ltd',
    vatId: '*********',
    addressHebrew: 'רחוב הרצל 123, תל אביב',
    addressEnglish: '123 Herzl Street, Tel Aviv',
    cityHebrew: 'תל אביב',
    cityEnglish: 'Tel Aviv',
    postalCode: '6473424',
    phone: '03-1234567',
    email: '<EMAIL>',
    logoUrl: '',
    industry: 'technology',
    annualRevenue: '1M-5M'
  })

  const [userSettings, setUserSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    weeklyReports: true,
    monthlyReports: true,
    expenseAlerts: true,
    invoiceReminders: true
  })

  const [subscriptionData] = useState({
    tier: 'free',
    expiresAt: null,
    features: {
      unlimitedInvoices: true,
      maxUsers: 5,
      aiFeatures: false,
      advancedReports: false,
      prioritySupport: false
    }
  })

  const handleSaveCompany = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement company data save
      console.log('Save company data:', companyData)
    } catch (error) {
      console.error('Error saving company data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveNotifications = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement notification settings save
      console.log('Save notification settings:', userSettings)
    } catch (error) {
      console.error('Error saving notification settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderCompanySettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>פרטי החברה</CardTitle>
          <CardDescription>עדכן את פרטי החברה שלך</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="nameHebrew">שם החברה (עברית)</Label>
              <Input
                id="nameHebrew"
                value={companyData.nameHebrew}
                onChange={(e) => setCompanyData({...companyData, nameHebrew: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <Label htmlFor="nameEnglish">שם החברה (אנגלית)</Label>
              <Input
                id="nameEnglish"
                value={companyData.nameEnglish}
                onChange={(e) => setCompanyData({...companyData, nameEnglish: e.target.value})}
                className="text-left"
                dir="ltr"
              />
            </div>
            
            <div>
              <Label htmlFor="businessNumber">מספר עסק</Label>
              <Input
                id="businessNumber"
                value={companyData.businessNumber}
                onChange={(e) => setCompanyData({...companyData, businessNumber: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <Label htmlFor="vatId">עוסק מורשה</Label>
              <Input
                id="vatId"
                value={companyData.vatId}
                onChange={(e) => setCompanyData({...companyData, vatId: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <Label htmlFor="phone">טלפון</Label>
              <Input
                id="phone"
                value={companyData.phone}
                onChange={(e) => setCompanyData({...companyData, phone: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <Label htmlFor="email">אימייל</Label>
              <Input
                id="email"
                type="email"
                value={companyData.email}
                onChange={(e) => setCompanyData({...companyData, email: e.target.value})}
                className="text-left"
                dir="ltr"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="addressHebrew">כתובת (עברית)</Label>
            <Input
              id="addressHebrew"
              value={companyData.addressHebrew}
              onChange={(e) => setCompanyData({...companyData, addressHebrew: e.target.value})}
              className="text-right"
              dir="rtl"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="cityHebrew">עיר</Label>
              <Input
                id="cityHebrew"
                value={companyData.cityHebrew}
                onChange={(e) => setCompanyData({...companyData, cityHebrew: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
            
            <div>
              <Label htmlFor="postalCode">מיקוד</Label>
              <Input
                id="postalCode"
                value={companyData.postalCode}
                onChange={(e) => setCompanyData({...companyData, postalCode: e.target.value})}
                className="text-right"
                dir="rtl"
              />
            </div>
          </div>
          
          <Button onClick={handleSaveCompany} disabled={isLoading}>
            {isLoading ? 'שומר...' : 'שמור שינויים'}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>לוגו החברה</CardTitle>
          <CardDescription>העלה לוגו שיופיע על המסמכים</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="w-20 h-20 border-2 border-dashed border-border rounded-lg flex items-center justify-center">
              {companyData.logoUrl ? (
                <img src={companyData.logoUrl} alt="Company Logo" className="w-full h-full object-contain" />
              ) : (
                <Building className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <Button variant="outline">
                <Upload className="ml-2 h-4 w-4" />
                העלה לוגו
              </Button>
              {companyData.logoUrl && (
                <Button variant="outline">
                  <Trash2 className="ml-2 h-4 w-4" />
                  הסר לוגו
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderNotificationSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle>הגדרות התראות</CardTitle>
        <CardDescription>בחר איך ומתי לקבל התראות</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="font-medium mb-4">התראות אימייל</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">התראות כלליות</p>
                <p className="text-sm text-muted-foreground">התראות על פעילות במערכת</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.emailNotifications}
                onChange={(e) => setUserSettings({...userSettings, emailNotifications: e.target.checked})}
                className="rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">דוחות שבועיים</p>
                <p className="text-sm text-muted-foreground">סיכום שבועי של הפעילות</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.weeklyReports}
                onChange={(e) => setUserSettings({...userSettings, weeklyReports: e.target.checked})}
                className="rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">דוחות חודשיים</p>
                <p className="text-sm text-muted-foreground">דוח מע"מ ופעילות חודשית</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.monthlyReports}
                onChange={(e) => setUserSettings({...userSettings, monthlyReports: e.target.checked})}
                className="rounded"
              />
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-4">התראות SMS</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">התראות דחופות</p>
                <p className="text-sm text-muted-foreground">התראות חשובות בלבד</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.smsNotifications}
                onChange={(e) => setUserSettings({...userSettings, smsNotifications: e.target.checked})}
                className="rounded"
              />
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-4">התראות עסקיות</h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">התראות הוצאות</p>
                <p className="text-sm text-muted-foreground">הוצאות חדשות שנסרקו</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.expenseAlerts}
                onChange={(e) => setUserSettings({...userSettings, expenseAlerts: e.target.checked})}
                className="rounded"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">תזכורות חשבוניות</p>
                <p className="text-sm text-muted-foreground">תזכורות על חשבוניות שטרם שולמו</p>
              </div>
              <input
                type="checkbox"
                checked={userSettings.invoiceReminders}
                onChange={(e) => setUserSettings({...userSettings, invoiceReminders: e.target.checked})}
                className="rounded"
              />
            </div>
          </div>
        </div>

        <Button onClick={handleSaveNotifications} disabled={isLoading}>
          {isLoading ? 'שומר...' : 'שמור הגדרות'}
        </Button>
      </CardContent>
    </Card>
  )

  const renderSubscriptionSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>מנוי נוכחי</CardTitle>
          <CardDescription>פרטי המנוי והתכונות הזמינות</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium">תכנית חינמית</h3>
              <p className="text-muted-foreground">מנוי בסיסי ללא עלות</p>
            </div>
            <Badge variant="secondary">חינמי</Badge>
          </div>
          
          <div className="space-y-3 mb-6">
            <div className="flex items-center justify-between">
              <span>חשבוניות ללא הגבלה</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>עד 5 משתמשים</span>
              <Badge variant="default">✓</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>תכונות AI</span>
              <Badge variant="secondary">לא זמין</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>דוחות מתקדמים</span>
              <Badge variant="secondary">לא זמין</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>תמיכה מועדפת</span>
              <Badge variant="secondary">לא זמין</Badge>
            </div>
          </div>
          
          <Button className="w-full">
            שדרג לתכנית מתקדמת - ₪99/חודש
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>ניהול חשבון</CardTitle>
          <CardDescription>פעולות חשבון ונתונים</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-border rounded-lg">
            <div>
              <p className="font-medium">ייצא נתונים</p>
              <p className="text-sm text-muted-foreground">הורד את כל הנתונים שלך</p>
            </div>
            <Button variant="outline">
              <Download className="ml-2 h-4 w-4" />
              ייצא
            </Button>
          </div>
          
          <div className="flex items-center justify-between p-4 border border-destructive rounded-lg">
            <div>
              <p className="font-medium text-destructive">מחק חשבון</p>
              <p className="text-sm text-muted-foreground">מחיקה מלאה של החשבון והנתונים</p>
            </div>
            <Button variant="destructive">
              <Trash2 className="ml-2 h-4 w-4" />
              מחק
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">הגדרות</h1>
          <p className="text-muted-foreground">נהל את הגדרות החשבון והמערכת</p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-border">
            <nav className="flex space-x-8 space-x-reverse">
              <button
                onClick={() => setActiveTab('company')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'company'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <Building className="inline-block ml-2 h-4 w-4" />
                פרטי חברה
              </button>
              <button
                onClick={() => setActiveTab('notifications')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'notifications'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <Bell className="inline-block ml-2 h-4 w-4" />
                התראות
              </button>
              <button
                onClick={() => setActiveTab('subscription')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'subscription'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <CreditCard className="inline-block ml-2 h-4 w-4" />
                מנוי וחשבון
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'company' && renderCompanySettings()}
        {activeTab === 'notifications' && renderNotificationSettings()}
        {activeTab === 'subscription' && renderSubscriptionSettings()}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/settings')({
  component: SettingsPage,
})
