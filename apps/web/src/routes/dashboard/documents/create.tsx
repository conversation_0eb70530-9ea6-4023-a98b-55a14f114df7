import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Progress } from '@fintech/ui'
import { Search, Plus, Trash2, ArrowRight, ArrowLeft } from 'lucide-react'

interface Customer {
  id: string
  name: string
  businessNumber: string
  email?: string
  phone?: string
}

interface DocumentItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  vatRate: number
  total: number
}

function CreateDocumentPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [items, setItems] = useState<DocumentItem[]>([
    { id: '1', description: '', quantity: 1, unitPrice: 0, vatRate: 18, total: 0 }
  ])
  const [documentType] = useState('tax_invoice') // From URL params
  const [issueDate, setIssueDate] = useState(new Date().toISOString().split('T')[0])
  const [dueDate, setDueDate] = useState('')
  const [notes, setNotes] = useState('')

  const steps = ['בחירת לקוח', 'פריטים', 'סיכום', 'שליחה']
  const progress = (currentStep / steps.length) * 100

  // Mock customers data
  const customers: Customer[] = [
    { id: '1', name: 'חברת דוגמה בע"מ', businessNumber: '*********', email: '<EMAIL>' },
    { id: '2', name: 'לקוח שני בע"מ', businessNumber: '*********', email: '<EMAIL>' },
  ]

  const filteredCustomers = customers.filter(customer =>
    customer.name.includes(searchQuery) || customer.businessNumber.includes(searchQuery)
  )

  const calculateItemTotal = (item: DocumentItem) => {
    const subtotal = item.quantity * item.unitPrice
    const vatAmount = subtotal * (item.vatRate / 100)
    return subtotal + vatAmount
  }

  const updateItem = (id: string, field: keyof DocumentItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updated = { ...item, [field]: value }
        updated.total = calculateItemTotal(updated)
        return updated
      }
      return item
    }))
  }

  const addItem = () => {
    const newItem: DocumentItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      vatRate: 18,
      total: 0
    }
    setItems([...items, newItem])
  }

  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id))
    }
  }

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    const vatAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice * item.vatRate / 100), 0)
    const total = subtotal + vatAmount
    return { subtotal, vatAmount, total }
  }

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="relative">
        <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="חפש לפי שם או מספר עסק"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pr-10 text-right"
          dir="rtl"
        />
      </div>

      <div className="grid gap-4">
        {filteredCustomers.length > 0 ? (
          filteredCustomers.map((customer) => (
            <Card 
              key={customer.id}
              className={`cursor-pointer transition-colors ${
                selectedCustomer?.id === customer.id ? 'border-primary bg-accent' : 'hover:bg-accent'
              }`}
              onClick={() => setSelectedCustomer(customer)}
            >
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{customer.name}</h3>
                    <p className="text-sm text-muted-foreground">ח.פ: {customer.businessNumber}</p>
                    {customer.email && (
                      <p className="text-sm text-muted-foreground">{customer.email}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">לא נמצאו לקוחות</p>
            <Button variant="outline">
              <Plus className="ml-2 h-4 w-4" />
              הוסף לקוח חדש
            </Button>
          </div>
        )}
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        {items.map((item, index) => (
          <Card key={item.id}>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                <div className="md:col-span-2">
                  <Label>תיאור</Label>
                  <Input
                    value={item.description}
                    onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                    placeholder="תיאור הפריט"
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div>
                  <Label>כמות</Label>
                  <Input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div>
                  <Label>מחיר יחידה</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={item.unitPrice}
                    onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div>
                  <Label>מע"מ (%)</Label>
                  <Input
                    type="number"
                    value={item.vatRate}
                    onChange={(e) => updateItem(item.id, 'vatRate', parseFloat(e.target.value) || 0)}
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="font-medium">₪{item.total.toFixed(2)}</span>
                  {items.length > 1 && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeItem(item.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Button variant="outline" onClick={addItem} className="w-full">
        <Plus className="ml-2 h-4 w-4" />
        הוסף פריט
      </Button>

      <Card>
        <CardContent className="p-4">
          <div className="space-y-2 text-left">
            <div className="flex justify-between">
              <span>סה"כ לפני מע"מ:</span>
              <span>₪{calculateTotals().subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>מע"מ:</span>
              <span>₪{calculateTotals().vatAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>סה"כ לתשלום:</span>
              <span>₪{calculateTotals().total.toFixed(2)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>פרטי המסמך</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="issueDate">תאריך הנפקה</Label>
              <Input
                id="issueDate"
                type="date"
                value={issueDate}
                onChange={(e) => setIssueDate(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="dueDate">תאריך פירעון</Label>
              <Input
                id="dueDate"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="notes">הערות</Label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full p-2 border border-input bg-background rounded-md text-right"
              dir="rtl"
              rows={3}
              placeholder="הערות נוספות..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Document Preview */}
      <Card>
        <CardHeader>
          <CardTitle>תצוגה מקדימה</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-white p-6 border rounded-lg text-black" dir="rtl">
            <div className="text-center mb-6">
              <h2 className="text-xl font-bold">חשבונית מס</h2>
              <p>מספר: INV-2025-0001</p>
            </div>
            
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="font-bold mb-2">מאת:</h3>
                <p>חברת דוגמה בע"מ</p>
                <p>ח.פ: *********</p>
              </div>
              
              <div>
                <h3 className="font-bold mb-2">עבור:</h3>
                <p>{selectedCustomer?.name}</p>
                <p>ח.פ: {selectedCustomer?.businessNumber}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 p-2 text-right">תיאור</th>
                    <th className="border border-gray-300 p-2 text-right">כמות</th>
                    <th className="border border-gray-300 p-2 text-right">מחיר</th>
                    <th className="border border-gray-300 p-2 text-right">סה"כ</th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td className="border border-gray-300 p-2">{item.description}</td>
                      <td className="border border-gray-300 p-2">{item.quantity}</td>
                      <td className="border border-gray-300 p-2">₪{item.unitPrice}</td>
                      <td className="border border-gray-300 p-2">₪{item.total.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="text-left">
              <p>סה"כ לפני מע"מ: ₪{calculateTotals().subtotal.toFixed(2)}</p>
              <p>מע"מ: ₪{calculateTotals().vatAmount.toFixed(2)}</p>
              <p className="font-bold text-lg">סה"כ לתשלום: ₪{calculateTotals().total.toFixed(2)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderStep4 = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>אפשרויות שליחה</CardTitle>
          <CardDescription>בחר כיצד לשלוח את המסמך ללקוח</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <Card className="cursor-pointer hover:bg-accent">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="flex-1">
                    <h3 className="font-medium">אימייל</h3>
                    <p className="text-sm text-muted-foreground">
                      שלח ללקוח באימייל: {selectedCustomer?.email}
                    </p>
                  </div>
                  <Button variant="outline">שלח</Button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-accent">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="flex-1">
                    <h3 className="font-medium">WhatsApp</h3>
                    <p className="text-sm text-muted-foreground">
                      שלח ללקוח בוואטסאפ
                    </p>
                  </div>
                  <Button variant="outline">שלח</Button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-accent">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="flex-1">
                    <h3 className="font-medium">הורד PDF</h3>
                    <p className="text-sm text-muted-foreground">
                      הורד את המסמך כקובץ PDF
                    </p>
                  </div>
                  <Button variant="outline">הורד</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">יצירת חשבונית חדשה</CardTitle>
            <CardDescription>
              צור חשבונית מס חדשה עם כל הפרטים הנדרשים
            </CardDescription>
            
            {/* Progress Indicator */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-muted-foreground mb-2">
                {steps.map((step, index) => (
                  <span 
                    key={index}
                    className={currentStep > index ? 'text-primary font-medium' : ''}
                  >
                    {step}
                  </span>
                ))}
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardHeader>
          
          <CardContent>
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
            
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1}
              >
                <ArrowRight className="ml-2 h-4 w-4" />
                הקודם
              </Button>
              
              {currentStep < 4 ? (
                <Button 
                  onClick={handleNext}
                  disabled={currentStep === 1 && !selectedCustomer}
                >
                  הבא
                  <ArrowLeft className="mr-2 h-4 w-4" />
                </Button>
              ) : (
                <Button>
                  סיים ושמור
                  <ArrowLeft className="mr-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/documents/create')({
  component: CreateDocumentPage,
})
