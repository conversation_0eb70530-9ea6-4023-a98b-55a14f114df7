import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Plus } from 'lucide-react'

interface StatCardProps {
  title: string
  value: string
  subtitle?: string
  change?: string
  action?: () => void
  actionText?: string
}

function StatCard({ title, value, subtitle, change, action, actionText }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && (
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        )}
        {change && (
          <p className="text-xs text-green-600 mt-1">{change}</p>
        )}
        {action && actionText && (
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={action}
          >
            {actionText}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

function DashboardIndex() {
  const stats = [
    {
      title: 'הכנסות החודש',
      value: '₪45,231',
      change: '+20.1% מהחודש הקודם',
      subtitle: 'ינואר 2025'
    },
    {
      title: 'מע"מ לתשלום',
      value: '₪8,142',
      subtitle: 'תאריך תשלום: 15/02/2025'
    },
    {
      title: 'חשבוניות פתוחות',
      value: '12',
      subtitle: 'סה"כ ₪23,450'
    },
    {
      title: 'הוצאות ממתינות',
      value: '5',
      action: () => console.log('Review expenses'),
      actionText: 'בדוק'
    }
  ]

  const recentDocuments = [
    {
      id: '1',
      number: 'INV-2025-0001',
      type: 'חשבונית מס',
      customer: 'לקוח דוגמה בע"מ',
      amount: '₪5,000',
      status: 'נשלח'
    },
    {
      id: '2', 
      number: 'REC-2025-0001',
      type: 'קבלה',
      customer: 'לקוח אחר בע"מ',
      amount: '₪3,200',
      status: 'טיוטה'
    }
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">לוח בקרה</h1>
        
        {/* Quick actions */}
        <div className="flex items-center space-x-2 space-x-reverse">
          <Button size="sm">
            <Plus size={16} className="ml-1" />
            חשבונית חדשה
          </Button>
        </div>
      </div>
      
      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>
      
      {/* Recent documents */}
      <Card>
        <CardHeader>
          <CardTitle>מסמכים אחרונים</CardTitle>
          <CardDescription>המסמכים שנוצרו לאחרונה</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentDocuments.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div>
                      <p className="font-medium">{doc.number}</p>
                      <p className="text-sm text-muted-foreground">{doc.type}</p>
                    </div>
                    <div>
                      <p className="text-sm">{doc.customer}</p>
                      <p className="text-sm font-medium">{doc.amount}</p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    doc.status === 'נשלח' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {doc.status}
                  </span>
                  <Button variant="ghost" size="sm">
                    צפה
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/')({
  component: DashboardIndex,
})
