import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Badge } from '@fintech/ui'
import { 
  Download, 
  FileText, 
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  AlertCircle
} from 'lucide-react'

interface VATReportData {
  period: string
  sales: {
    totalBeforeVat: number
    vatCollected: number
    totalWithVat: number
    documentsCount: number
  }
  purchases: {
    totalBeforeVat: number
    vatPaid: number
    totalWithVat: number
    expensesCount: number
  }
  vatLiability: number
  paymentDueDate: string
}

function ReportsPage() {
  const [activeTab, setActiveTab] = useState('vat')
  const [periodStart, setPeriodStart] = useState('2025-01-01')
  const [periodEnd, setPeriodEnd] = useState('2025-01-31')

  // Mock VAT report data
  const vatReport: VATReportData = {
    period: 'ינואר 2025',
    sales: {
      totalBeforeVat: 45231,
      vatCollected: 8142,
      totalWithVat: 53373,
      documentsCount: 23
    },
    purchases: {
      totalBeforeVat: 12450,
      vatPaid: 2241,
      totalWithVat: 14691,
      expensesCount: 15
    },
    vatLiability: 5901, // vatCollected - vatPaid
    paymentDueDate: '2025-02-15'
  }

  const salesDocuments = [
    { id: '1', number: 'INV-2025-001', customer: 'לקוח א בע"מ', amount: 5000, vat: 900, date: '2025-01-15' },
    { id: '2', number: 'INV-2025-002', customer: 'לקוח ב בע"מ', amount: 3200, vat: 576, date: '2025-01-16' },
    { id: '3', number: 'INV-2025-003', customer: 'לקוח ג בע"מ', amount: 7500, vat: 1350, date: '2025-01-18' }
  ]

  const purchaseExpenses = [
    { id: '1', vendor: 'ספק א בע"מ', amount: 2000, vat: 360, date: '2025-01-10', category: 'ציוד משרדי' },
    { id: '2', vendor: 'ספק ב בע"מ', amount: 1500, vat: 270, date: '2025-01-12', category: 'שירותים' },
    { id: '3', vendor: 'ספק ג בע"מ', amount: 3000, vat: 540, date: '2025-01-14', category: 'שיווק' }
  ]

  const handleExportPDF = () => {
    console.log('Export VAT report as PDF')
    // TODO: Implement PDF export
  }

  const handleExportExcel = () => {
    console.log('Export VAT report as Excel')
    // TODO: Implement Excel export
  }

  const handleExportPCN874 = () => {
    console.log('Export PCN874 format')
    // TODO: Implement PCN874 export for accountants
  }

  const renderVATReport = () => (
    <div className="space-y-6">
      {/* Period Selection */}
      <Card>
        <CardHeader>
          <CardTitle>בחירת תקופה</CardTitle>
          <CardDescription>בחר את התקופה לדוח המע"מ</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="periodStart">מתאריך</Label>
              <Input
                id="periodStart"
                type="date"
                value={periodStart}
                onChange={(e) => setPeriodStart(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="periodEnd">עד תאריך</Label>
              <Input
                id="periodEnd"
                type="date"
                value={periodEnd}
                onChange={(e) => setPeriodEnd(e.target.value)}
              />
            </div>
            
            <div className="flex items-end">
              <Button className="w-full">
                עדכן דוח
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* VAT Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">מע"מ שנגבה</p>
                <p className="text-2xl font-bold text-green-600">₪{vatReport.sales.vatCollected.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">{vatReport.sales.documentsCount} מסמכים</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <TrendingDown className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">מע"מ שולם</p>
                <p className="text-2xl font-bold text-blue-600">₪{vatReport.purchases.vatPaid.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">{vatReport.purchases.expensesCount} הוצאות</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <DollarSign className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">חבות מע"מ</p>
                <p className="text-2xl font-bold text-orange-600">₪{vatReport.vatLiability.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">לתשלום</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <AlertCircle className="h-8 w-8 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">תאריך תשלום</p>
                <p className="text-lg font-bold">{new Date(vatReport.paymentDueDate).toLocaleDateString('he-IL')}</p>
                <p className="text-xs text-red-600">עד 15 לחודש</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardHeader>
          <CardTitle>ייצוא דוחות</CardTitle>
          <CardDescription>ייצא את דוח המע"מ בפורמטים שונים</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4 space-x-reverse">
            <Button onClick={handleExportPDF} variant="outline">
              <FileText className="ml-2 h-4 w-4" />
              ייצא PDF
            </Button>
            <Button onClick={handleExportExcel} variant="outline">
              <Download className="ml-2 h-4 w-4" />
              ייצא Excel
            </Button>
            <Button onClick={handleExportPCN874} variant="outline">
              <FileText className="ml-2 h-4 w-4" />
              ייצא PCN874
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sales Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle>מסמכי מכירות</CardTitle>
          <CardDescription>חשבוניות וקבלות שהונפקו בתקופה</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-2 font-medium">מספר מסמך</th>
                  <th className="text-right p-2 font-medium">לקוח</th>
                  <th className="text-right p-2 font-medium">תאריך</th>
                  <th className="text-right p-2 font-medium">סכום לפני מע"מ</th>
                  <th className="text-right p-2 font-medium">מע"מ</th>
                  <th className="text-right p-2 font-medium">סה"כ</th>
                </tr>
              </thead>
              <tbody>
                {salesDocuments.map((doc) => (
                  <tr key={doc.id} className="border-b hover:bg-muted/50">
                    <td className="p-2">{doc.number}</td>
                    <td className="p-2">{doc.customer}</td>
                    <td className="p-2">{new Date(doc.date).toLocaleDateString('he-IL')}</td>
                    <td className="p-2">₪{doc.amount.toLocaleString()}</td>
                    <td className="p-2">₪{doc.vat.toLocaleString()}</td>
                    <td className="p-2 font-medium">₪{(doc.amount + doc.vat).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="border-t-2 font-bold">
                  <td className="p-2" colSpan={3}>סה"כ</td>
                  <td className="p-2">₪{vatReport.sales.totalBeforeVat.toLocaleString()}</td>
                  <td className="p-2">₪{vatReport.sales.vatCollected.toLocaleString()}</td>
                  <td className="p-2">₪{vatReport.sales.totalWithVat.toLocaleString()}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Purchase Expenses Table */}
      <Card>
        <CardHeader>
          <CardTitle>הוצאות רכישה</CardTitle>
          <CardDescription>הוצאות שאושרו בתקופה</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-2 font-medium">ספק</th>
                  <th className="text-right p-2 font-medium">קטגוריה</th>
                  <th className="text-right p-2 font-medium">תאריך</th>
                  <th className="text-right p-2 font-medium">סכום לפני מע"מ</th>
                  <th className="text-right p-2 font-medium">מע"מ</th>
                  <th className="text-right p-2 font-medium">סה"כ</th>
                </tr>
              </thead>
              <tbody>
                {purchaseExpenses.map((expense) => (
                  <tr key={expense.id} className="border-b hover:bg-muted/50">
                    <td className="p-2">{expense.vendor}</td>
                    <td className="p-2">{expense.category}</td>
                    <td className="p-2">{new Date(expense.date).toLocaleDateString('he-IL')}</td>
                    <td className="p-2">₪{expense.amount.toLocaleString()}</td>
                    <td className="p-2">₪{expense.vat.toLocaleString()}</td>
                    <td className="p-2 font-medium">₪{(expense.amount + expense.vat).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="border-t-2 font-bold">
                  <td className="p-2" colSpan={3}>סה"כ</td>
                  <td className="p-2">₪{vatReport.purchases.totalBeforeVat.toLocaleString()}</td>
                  <td className="p-2">₪{vatReport.purchases.vatPaid.toLocaleString()}</td>
                  <td className="p-2">₪{vatReport.purchases.totalWithVat.toLocaleString()}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderIncomeStatement = () => (
    <Card>
      <CardContent className="p-8 text-center">
        <h3 className="text-lg font-medium mb-2">דוח רווח והפסד</h3>
        <p className="text-muted-foreground">בפיתוח - יהיה זמין בקרוב</p>
      </CardContent>
    </Card>
  )

  const renderCustomerSummary = () => (
    <Card>
      <CardContent className="p-8 text-center">
        <h3 className="text-lg font-medium mb-2">סיכום לקוחות</h3>
        <p className="text-muted-foreground">בפיתוח - יהיה זמין בקרוב</p>
      </CardContent>
    </Card>
  )

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">דוחות</h1>
          <p className="text-muted-foreground">צפה וייצא דוחות עסקיים ומע"מ</p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-border">
            <nav className="flex space-x-8 space-x-reverse">
              <button
                onClick={() => setActiveTab('vat')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'vat'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                דוח מע"מ
              </button>
              <button
                onClick={() => setActiveTab('income')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'income'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                רווח והפסד
              </button>
              <button
                onClick={() => setActiveTab('customers')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'customers'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                סיכום לקוחות
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'vat' && renderVATReport()}
        {activeTab === 'income' && renderIncomeStatement()}
        {activeTab === 'customers' && renderCustomerSummary()}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard/reports')({
  component: ReportsPage,
})
