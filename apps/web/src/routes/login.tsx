import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'

function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    try {
      // TODO: Implement Supabase authentication
      console.log('Login attempt:', { email, password })
    } catch (error) {
      console.error('Login error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background cosmic-grid px-4">
      <Card className="w-full max-w-md cosmic-glass">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold font-secular">התחברות</CardTitle>
          <CardDescription>
            הכנס את פרטי החשבון שלך כדי להתחבר
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">כתובת אימייל</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="text-right"
                dir="rtl"
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">סיסמה</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="text-right"
                dir="rtl"
                placeholder="••••••••"
                required
              />
            </div>

            <Button 
              type="submit" 
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? 'מתחבר...' : 'התחבר'}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              אין לך חשבון?{' '}
              <Link 
                to="/register" 
                className="text-primary hover:underline font-medium"
              >
                הירשם כאן
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/login')({
  component: LoginPage,
})
