import { create<PERSON>ile<PERSON><PERSON><PERSON>, <PERSON>, Outlet } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { 
  LayoutDashboard, 
  FileText, 
  Users, 
  Package, 
  CreditCard, 
  BarChart3, 
  Settings,
  Plus,
  LogOut,
  Menu,
  X
} from 'lucide-react'

interface StatCardProps {
  title: string
  value: string
  subtitle?: string
  change?: string
  action?: () => void
  actionText?: string
}

function StatCard({ title, value, subtitle, change, action, actionText }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && (
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        )}
        {change && (
          <p className="text-xs text-green-600 mt-1">{change}</p>
        )}
        {action && actionText && (
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={action}
          >
            {actionText}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}

function Sidebar({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const navigation = [
    { name: 'לוח בקרה', href: '/dashboard', icon: LayoutDashboard },
    { 
      name: 'מסמכים', 
      href: '/dashboard/documents', 
      icon: FileText,
      children: [
        { name: 'חשבוניות', href: '/dashboard/documents/invoices' },
        { name: 'קבלות', href: '/dashboard/documents/receipts' },
        { name: 'זיכויים', href: '/dashboard/documents/credits' }
      ]
    },
    { name: 'לקוחות', href: '/dashboard/customers', icon: Users },
    { name: 'מוצרים', href: '/dashboard/products', icon: Package },
    { name: 'הוצאות', href: '/dashboard/expenses', icon: CreditCard, badge: '5' },
    { name: 'דוחות', href: '/dashboard/reports', icon: BarChart3 },
    { name: 'הגדרות', href: '/dashboard/settings', icon: Settings }
  ]

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed top-0 right-0 h-full w-64 bg-card border-l border-border z-50 transform transition-transform duration-300 ease-in-out cosmic-glass
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        md:translate-x-0 md:static md:z-auto
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold font-secular">חברת דוגמה בע"מ</h2>
                <p className="text-sm text-muted-foreground">מנהל</p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={onClose}
              >
                <X size={20} />
              </Button>
            </div>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {navigation.map((item) => (
              <div key={item.name}>
                <Link
                  to={item.href}
                  className="flex items-center px-3 py-2 text-sm rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                  activeProps={{
                    className: "bg-accent text-accent-foreground"
                  }}
                >
                  <item.icon size={18} className="ml-3" />
                  {item.name}
                  {item.badge && (
                    <span className="mr-auto bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
                
                {item.children && (
                  <div className="mr-6 mt-1 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.name}
                        to={child.href}
                        className="block px-3 py-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        {child.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
          
          {/* User menu */}
          <div className="p-4 border-t border-border">
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => console.log('Logout')}
            >
              <LogOut size={18} className="ml-3" />
              התנתק
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

function DashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="flex h-screen bg-background cosmic-grid font-secular">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-card border-b border-border px-6 py-4 cosmic-glass">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu size={20} />
            </Button>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-auto">
          <Outlet />
        </main>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/dashboard')({
  component: DashboardLayout,
})
