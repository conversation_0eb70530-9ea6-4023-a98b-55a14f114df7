{"name": "@fintech/web", "private": true, "version": "1.0.0", "description": "Main fintech web application", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@fintech/ui": "file:../../packages/ui", "@fintech/types": "file:../../packages/types", "@fintech/utils": "file:../../packages/utils", "@supabase/supabase-js": "^2.45.4", "@tanstack/react-query": "^5.56.2", "@tanstack/react-router": "^1.58.3", "@tanstack/react-router-devtools": "^1.58.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "sonner": "^1.5.0", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^2.0.5"}}