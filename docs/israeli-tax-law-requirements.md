# Israeli Tax Law Requirements for Invoicing Software

## Document Types Overview

### 1. חשבונית מס (Tax Invoice) - Code 305
**When to use:** For VAT-registered businesses selling to other businesses or consumers
**Purpose:** Official tax document for VAT reporting

**Required Fields:**
- Business name and address
- Tax ID number (מספר עוסק מורשה)
- Invoice number (sequential)
- Date of issue
- Customer details (name, address, tax ID if business)
- Description of goods/services
- Quantity and unit price
- Subtotal before VAT
- VAT rate (18% standard rate)
- VAT amount
- Total amount including VAT
- Payment terms
- Authorized signature

### 2. קבלה (Receipt) - Code 400
**When to use:** For cash transactions, proof of payment
**Purpose:** Confirms payment received

**Required Fields:**
- Business name and address
- Tax ID number
- Receipt number (sequential)
- Date of payment
- Customer name (if known)
- Description of payment
- Amount received
- Payment method
- Authorized signature

### 3. חשבונית מס-קבלה (Tax Invoice-Receipt) - Code 320
**When to use:** Combined document when payment is immediate
**Purpose:** Both invoice and receipt in one document

**Required Fields:**
- All fields from Tax Invoice
- All fields from Receipt
- Clear indication it serves both purposes
- Payment confirmation details

### 4. הצעת מחיר (Quote/Estimate) - Code 200
**When to use:** Before providing services or selling goods
**Purpose:** Formal price quotation

**Required Fields:**
- Business name and address
- Quote number
- Date of quote
- Validity period
- Customer details
- Description of goods/services
- Quantities and unit prices
- Subtotal
- VAT calculation
- Total amount
- Terms and conditions

## VAT (מע"מ) Requirements

### Standard VAT Rate: 18%
### Zero-rated items: Exports, certain financial services
### Exempt items: Medical services, education, etc.

### VAT Calculation Rules:
1. Calculate subtotal of all taxable items
2. Apply 18% VAT to taxable subtotal
3. Add VAT to subtotal for final amount
4. Round to nearest agora (0.01 NIS)

## Document Numbering Requirements

### Sequential Numbering:
- Each document type must have sequential numbering
- No gaps allowed in sequence
- Format: Can be numeric or alphanumeric
- Must be unique per document type
- Reset annually is permitted

### Recommended Format:
- Tax Invoice: INV-2024-001, INV-2024-002, etc.
- Receipt: REC-2024-001, REC-2024-002, etc.
- Quote: QUO-2024-001, QUO-2024-002, etc.

## Digital Document Requirements

### Electronic Invoicing:
- Must be approved by Tax Authority
- Digital signature required
- PDF format acceptable
- Must maintain original formatting
- Backup requirements apply

### Storage Requirements:
- Keep for 7 years minimum
- Digital copies acceptable
- Must be accessible for audit
- Backup and recovery procedures required

## Special Considerations

### Foreign Currency:
- Must show NIS equivalent
- Exchange rate and date required
- Bank of Israel rates preferred

### Advance Payments:
- Separate receipt for advance
- Final invoice shows advance deduction
- Clear reference between documents

### Credit Notes:
- Reference original invoice
- Sequential numbering required
- Clear reason for credit
- Proper VAT adjustment

## Compliance Notes

### Tax Authority Reporting:
- Monthly VAT returns required
- Annual income tax returns
- Digital reporting preferred
- Penalties for non-compliance

### Audit Requirements:
- All documents must be available
- Electronic systems must be auditable
- Proper internal controls required
- Documentation of procedures needed

## Implementation Guidelines for Software

### Database Schema Considerations:
1. Document type field (305, 320, 400, 200)
2. Sequential numbering per type
3. VAT calculation fields
4. Customer tax status tracking
5. Payment status tracking
6. Digital signature fields

### User Interface Requirements:
1. Clear document type selection
2. Automatic VAT calculation
3. Customer database integration
4. Sequential number generation
5. Print/PDF generation
6. Search and filtering capabilities

### Validation Rules:
1. Required field validation
2. VAT calculation verification
3. Sequential number checking
4. Customer tax ID validation
5. Date format compliance
6. Amount calculation accuracy

This documentation serves as the foundation for implementing compliant Israeli invoicing software.
