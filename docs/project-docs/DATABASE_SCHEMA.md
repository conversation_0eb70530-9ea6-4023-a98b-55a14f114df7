## **4. DATABASE_SCHEMA.md**

```markdown
# Database Schema - Israeli Invoice App

## Enums

```sql
-- Subscription tiers
CREATE TYPE subscription_tier AS ENUM ('free', 'paid');

-- User roles
CREATE TYPE user_role AS ENUM ('admin', 'user', 'accountant');

-- Document types
CREATE TYPE document_type AS ENUM (
  'tax_invoice',
  'receipt', 
  'credit_note',
  'tax_invoice_receipt'
);

-- Document status
CREATE TYPE document_status AS ENUM (
  'draft',
  'pending_allocation',
  'approved',
  'sent',
  'paid',
  'cancelled'
);

-- Expense status
CREATE TYPE expense_status AS ENUM ('pending', 'approved', 'rejected');

-- Expense categories
CREATE TYPE expense_category AS ENUM (
  'office_supplies',
  'travel',
  'utilities',
  'rent',
  'professional_services',
  'marketing',
  'equipment',
  'other'
);

-- Duplicate risk levels
CREATE TYPE duplicate_risk AS ENUM ('none', 'low', 'high');

-- Email providers
CREATE TYPE email_provider AS ENUM ('gmail', 'outlook');

-- ITA queue status
CREATE TYPE queue_status AS ENUM ('pending', 'processing', 'success', 'failed');
Core Tables
companies
SQL

CREATE TABLE companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  business_number VARCHAR(9) UNIQUE NOT NULL,
  name_hebrew VARCHAR(255) NOT NULL,
  name_english VARCHAR(255),
  vat_id VARCHAR(9) NOT NULL,
  address_hebrew TEXT NOT NULL,
  address_english TEXT,
  city_hebrew VARCHAR(100) NOT NULL,
  city_english VARCHAR(100),
  postal_code VARCHAR(7),
  phone VARCHAR(15) NOT NULL,
  email VARCHAR(255) NOT NULL,
  logo_url TEXT,
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_expires_at TIMESTAMP,
  industry VARCHAR(100) NOT NULL,
  annual_revenue VARCHAR(50),
  interested_in_loan BOOLEAN DEFAULT false,
  interested_in_insurance BOOLEAN DEFAULT false,
  interested_in_accounting BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_companies_business_number ON companies(business_number);
CREATE INDEX idx_companies_subscription ON companies(subscription_tier, subscription_expires_at);

-- RLS Policies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

CREATE POLICY companies_select ON companies FOR SELECT
  USING (id IN (SELECT company_id FROM company_users WHERE user_id = auth.uid()));

CREATE POLICY companies_update ON companies FOR UPDATE
  USING (id IN (SELECT company_id FROM company_users WHERE user_id = auth.uid() AND role = 'admin'));
users
SQL

CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  phone VARCHAR(15),
  created_at TIMESTAMP DEFAULT NOW(),
  last_login_at TIMESTAMP
);

-- RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY users_select ON users FOR SELECT
  USING (id = auth.uid() OR id IN (
    SELECT cu2.user_id FROM company_users cu1
    JOIN company_users cu2 ON cu1.company_id = cu2.company_id
    WHERE cu1.user_id = auth.uid()
  ));
company_users
SQL

CREATE TABLE company_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  role user_role NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  created_by UUID REFERENCES users(id),
  UNIQUE(company_id, user_id)
);

-- Indexes
CREATE INDEX idx_company_users_company ON company_users(company_id);
CREATE INDEX idx_company_users_role ON company_users(company_id, role);

-- RLS
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;

CREATE POLICY company_users_select ON company_users FOR SELECT
  USING (user_id = auth.uid() OR company_id IN (
    SELECT company_id FROM company_users WHERE user_id = auth.uid()
  ));
Document Tables
document_sequences
SQL

CREATE TABLE document_sequences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  document_type document_type NOT NULL,
  prefix VARCHAR(20),
  current_number INTEGER DEFAULT 0 NOT NULL,
  last_used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(company_id, document_type)
);

-- RLS
ALTER TABLE document_sequences ENABLE ROW LEVEL SECURITY;

CREATE POLICY sequences_select ON document_sequences FOR SELECT
  USING (company_id IN (SELECT company_id FROM company_users WHERE user_id = auth.uid()));

-- Note: Updates only through functions to ensure atomicity
documents
SQL

CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  document_type document_type NOT NULL,
  document_number VARCHAR(50) NOT NULL,
  customer_id UUID REFERENCES customers(id) NOT NULL,
  issue_date DATE NOT NULL,
  due_date DATE,
  currency VARCHAR(3) DEFAULT 'ILS',
  subtotal DECIMAL(12,2) NOT NULL,
  vat_amount DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL,
  status document_status DEFAULT 'draft',
  ita_allocation_number VARCHAR(50),
  ita_allocation_date TIMESTAMP,
  ita_submission_attempts INTEGER DEFAULT 0,
  ita_last_error TEXT,
  parent_document_id UUID REFERENCES documents(id),
  notes TEXT,
  template_id VARCHAR(50) DEFAULT 'default',
  pdf_url TEXT,
  sent_at TIMESTAMP,
  sent_via VARCHAR(20),
  created_by UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(company_id, document_number)
);

-- Indexes
CREATE INDEX idx_documents_company ON documents(company_id);
CREATE INDEX idx_documents_customer ON documents(customer_id);
CREATE INDEX idx_documents_status ON documents(company_id, status);
CREATE INDEX idx_documents_date ON documents(company_id, issue_date DESC);
CREATE INDEX idx_documents_allocation ON documents(ita_allocation_number) WHERE ita_allocation_number IS NOT NULL;

-- RLS
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_select ON documents FOR SELECT
  USING (company_id IN (SELECT company_id FROM company_users WHERE user_id = auth.uid()));
document_items
SQL

CREATE TABLE document_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID REFERENCES documents(id) ON DELETE CASCADE NOT NULL,
  product_id UUID REFERENCES products(id),
  line_number INTEGER NOT NULL,
  description_hebrew TEXT NOT NULL,
  description_english TEXT,
  quantity DECIMAL(10,3) NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'ILS',
  discount_percent DECIMAL(5,2) DEFAULT 0,
  vat_rate DECIMAL(4,2) DEFAULT 18.00,
  line_total DECIMAL(10,2) NOT NULL,
  vat_amount DECIMAL(10,2) NOT NULL,
  total_with_vat DECIMAL(10,2) NOT NULL
);

-- Indexes
CREATE INDEX idx_document_items_document ON document_items(document_id, line_number);
document_relationships
SQL

CREATE TABLE document_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  source_document_id UUID REFERENCES documents(id) NOT NULL,
  target_document_id UUID REFERENCES documents(id) NOT NULL,
  relationship_type VARCHAR(50) NOT NULL,
  amount_applied DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(source_document_id, target_document_id)
);
Customer & Product Tables
customers
SQL

CREATE TABLE customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  business_number VARCHAR(9) NOT NULL,
  name_hebrew VARCHAR(255) NOT NULL,
  name_english VARCHAR(255),
  vat_id VARCHAR(9),
  billing_address_hebrew TEXT NOT NULL,
  billing_address_english TEXT,
  shipping_address_hebrew TEXT,
  shipping_address_english TEXT,
  city_hebrew VARCHAR(100) NOT NULL,
  city_english VARCHAR(100),
  contact_name VARCHAR(255),
  contact_email VARCHAR(255),
  contact_phone VARCHAR(15),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_customers_company ON customers(company_id);
CREATE INDEX idx_customers_business_number ON customers(company_id, business_number);
products
SQL

CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  sku VARCHAR(100),
  name_hebrew VARCHAR(255) NOT NULL,
  name_english VARCHAR(255),
  description_hebrew TEXT,
  description_english TEXT,
  unit_price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'ILS',
  vat_rate DECIMAL(4,2) DEFAULT 18.00,
  is_service BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_products_company ON products(company_id);
CREATE INDEX idx_products_sku ON products(company_id, sku) WHERE sku IS NOT NULL;
Expense Tables
email_accounts
SQL

CREATE TABLE email_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  email_address VARCHAR(255) NOT NULL,
  provider email_provider NOT NULL,
  access_token TEXT NOT NULL, -- Encrypted
  refresh_token TEXT, -- Encrypted
  token_expires_at TIMESTAMP,
  last_sync_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- RLS
ALTER TABLE email_accounts ENABLE ROW LEVEL SECURITY;

CREATE POLICY email_accounts_select ON email_accounts FOR SELECT
  USING (company_id IN (
    SELECT company_id FROM company_users 
    WHERE user_id = auth.uid() AND role IN ('admin', 'accountant')
  ));
expenses
SQL

CREATE TABLE expenses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  expense_number VARCHAR(50) NOT NULL,
  vendor_name VARCHAR(255) NOT NULL,
  expense_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  vat_amount DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'ILS',
  category expense_category NOT NULL,
  description TEXT,
  status expense_status DEFAULT 'pending',
  duplicate_risk duplicate_risk DEFAULT 'none',
  duplicate_of_id UUID REFERENCES expenses(id),
  source VARCHAR(20) NOT NULL, -- 'manual', 'email_scan', 'upload'
  source_email_id UUID REFERENCES email_accounts(id),
  original_file_url TEXT,
  extracted_data JSONB,
  approved_by UUID REFERENCES users(id),
  approved_at TIMESTAMP,
  rejection_reason TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_expenses_company ON expenses(company_id);
CREATE INDEX idx_expenses_status ON expenses(company_id, status);
CREATE INDEX idx_expenses_date ON expenses(company_id, expense_date DESC);
CREATE INDEX idx_expenses_duplicate ON expenses(company_id, duplicate_risk) WHERE duplicate_risk != 'none';
System Tables
audit_log
SQL

CREATE TABLE audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  user_id UUID REFERENCES users(id) NOT NULL,
  action VARCHAR(50) NOT NULL,
  entity_type VARCHAR(50) NOT NULL,
  entity_id UUID NOT NULL,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_audit_company ON audit_log(company_id, created_at DESC);
CREATE INDEX idx_audit_entity ON audit_log(entity_type, entity_id);

-- RLS
ALTER TABLE audit_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY audit_select ON audit_log FOR SELECT
  USING (company_id IN (
    SELECT company_id FROM company_users 
    WHERE user_id = auth.uid() AND role IN ('admin', 'accountant')
  ));
ita_queue
SQL

CREATE TABLE ita_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID REFERENCES documents(id) NOT NULL,
  status queue_status DEFAULT 'pending',
  attempts INTEGER DEFAULT 0,
  next_retry_at TIMESTAMP,
  request_payload JSONB NOT NULL,
  response_payload JSONB,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_ita_queue_status ON ita_queue(status, next_retry_at) 
  WHERE status IN ('pending', 'processing');
Functions
get_next_document_number
SQL

CREATE OR REPLACE FUNCTION get_next_document_number(
  p_company_id UUID,
  p_document_type document_type
) RETURNS TABLE(
  full_number VARCHAR,
  sequence_number INTEGER,
  prefix VARCHAR
) AS $$
DECLARE
  v_sequence RECORD;
  v_next_number INTEGER;
  v_prefix VARCHAR;
  v_full_number VARCHAR;
BEGIN
  -- Lock the sequence row
  SELECT * INTO v_sequence
  FROM document_sequences
  WHERE company_id = p_company_id 
    AND document_type = p_document_type
  FOR UPDATE;
  
  -- Create sequence if doesn't exist
  IF NOT FOUND THEN
    INSERT INTO document_sequences (company_id, document_type, current_number)
    VALUES (p_company_id, p_document_type, 0)
    RETURNING * INTO v_sequence;
  END IF;
  
  -- Increment number
  v_next_number := v_sequence.current_number + 1;
  
  -- Update sequence
  UPDATE document_sequences
  SET current_number = v_next_number,
      last_used_at = NOW()
  WHERE id = v_sequence.id;
  
  -- Format number
  v_prefix := COALESCE(v_sequence.prefix, 
    CASE p_document_type
      WHEN 'tax_invoice' THEN 'INV-'
      WHEN 'receipt' THEN 'RCP-'
      WHEN 'credit_note' THEN 'CRD-'
      WHEN 'tax_invoice_receipt' THEN 'TIR-'
    END || EXTRACT(YEAR FROM NOW()) || '-'
  );
  
  v_full_number := v_prefix || LPAD(v_next_number::TEXT, 5, '0');
  
  RETURN QUERY
  SELECT v_full_number, v_next_number, v_prefix;
END;
$$ LANGUAGE plpgsql;
