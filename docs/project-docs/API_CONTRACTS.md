## **3. API_CONTRACTS.md**

```markdown
# API Contracts - Israeli Invoice App

## Base Configuration
Base URL: https://etrxcuxhsgufizgjdcro.supabase.co Authentication: Bearer token (Supabase JWT) Content-Type: application/json

text


## Authentication Endpoints

### Register Company
```typescript
POST /api/auth/register
Request: {
  email: string;
  password: string;
  full_name: string;
  phone: string;
  company: {
    business_number: string; // 9 digits
    name_hebrew: string;
    name_english?: string;
    vat_id: string; // 9 digits
    address_hebrew: string;
    city_hebrew: string;
    phone: string;
    industry: string;
    annual_revenue?: string;
    interested_in_loan: boolean;
    interested_in_insurance: boolean;
    interested_in_accounting: boolean;
  }
}

Response: {
  user: {
    id: string;
    email: string;
    full_name: string;
  };
  company: {
    id: string;
    business_number: string;
    name_hebrew: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  }
}

Error Responses:
- 400: Invalid input format
- 409: Email or business number already exists
- 500: Server error
Login
TypeScript

POST /api/auth/login
Request: {
  email: string;
  password: string;
}

Response: {
  user: User;
  companies: Company[];
  session: Session;
}
Document Management
Get Next Sequential Number
TypeScript

GET /api/documents/next-number
Query Parameters:
  - company_id: string (uuid)
  - document_type: 'tax_invoice' | 'receipt' | 'credit_note' | 'tax_invoice_receipt'

Response: {
  next_number: string; // e.g., "INV-2025-00001"
  prefix: string;
  sequence: number;
}
Create Document
TypeScript

POST /api/documents
Request: {
  company_id: string;
  document_type: string;
  customer_id: string;
  issue_date: string; // YYYY-MM-DD
  due_date?: string;
  currency: string; // Default: 'ILS'
  items: [{
    product_id?: string;
    description_hebrew: string;
    description_english?: string;
    quantity: number;
    unit_price: number;
    vat_rate: number; // Default: 18
    discount_percent?: number;
  }];
  notes?: string;
  template_id?: string;
}

Response: {
  document: {
    id: string;
    document_number: string;
    status: string;
    ita_allocation_number?: string;
    pdf_url?: string;
    total_amount: number;
  };
  ita_submission: {
    status: 'queued' | 'success' | 'failed';
    allocation_number?: string;
    error?: string;
  }
}
Send Document
TypeScript

POST /api/documents/{id}/send
Request: {
  method: 'email' | 'whatsapp';
  recipient_email?: string;
  recipient_phone?: string;
}

Response: {
  status: 'sent';
  sent_at: string;
  delivery_method: string;
}
List Documents
TypeScript

GET /api/documents
Query Parameters:
  - company_id: string
  - status?: string
  - document_type?: string
  - from_date?: string
  - to_date?: string
  - page?: number
  - limit?: number (max: 100)

Response: {
  documents: Document[];
  total: number;
  page: number;
  pages: number;
}
Customer Management
Create Customer
TypeScript

POST /api/customers
Request: {
  company_id: string;
  business_number: string;
  name_hebrew: string;
  name_english?: string;
  vat_id?: string;
  billing_address_hebrew: string;
  billing_address_english?: string;
  shipping_address_hebrew?: string;
  shipping_address_english?: string;
  city_hebrew: string;
  city_english?: string;
  contact_email?: string;
  contact_phone?: string;
  notes?: string;
}

Response: Customer
Search Customers
TypeScript

GET /api/customers/search
Query Parameters:
  - company_id: string
  - query: string (searches name and business number)
  - limit?: number (default: 20)

Response: Customer[]
Product Management
Create Product
TypeScript

POST /api/products
Request: {
  company_id: string;
  sku?: string;
  name_hebrew: string;
  name_english?: string;
  description_hebrew?: string;
  description_english?: string;
  unit_price: number;
  currency: string;
  vat_rate: number;
  is_service: boolean;
}

Response: Product
Expense Management
Connect Email Account
TypeScript

POST /api/email-accounts/connect
Request: {
  company_id: string;
  provider: 'gmail' | 'outlook';
  auth_code: string; // OAuth authorization code
}

Response: {
  id: string;
  email_address: string;
  provider: string;
  is_active: boolean;
  last_sync_at?: string;
}
Trigger Email Scan
TypeScript

POST /api/expenses/scan
Request: {
  company_id: string;
  email_account_id?: string;
}

Response: {
  scanned_count: number;
  new_expenses: number;
  duplicates_detected: number;
}
Update Expense Status
TypeScript

PATCH /api/expenses/{id}/status
Request: {
  status: 'approved' | 'rejected';
  rejection_reason?: string;
}

Response: Expense

Authorization: Must have 'accountant' role
List Expenses
TypeScript

GET /api/expenses
Query Parameters:
  - company_id: string
  - status?: 'pending' | 'approved' | 'rejected'
  - category?: string
  - duplicate_risk?: 'none' | 'low' | 'high'
  - from_date?: string
  - to_date?: string

Response: {
  expenses: Expense[];
  total: number;
  pending_count: number;
  total_amount: number;
}
Reporting
VAT Report
TypeScript

GET /api/reports/vat
Query Parameters:
  - company_id: string
  - period_start: string // YYYY-MM-DD
  - period_end: string

Response: {
  period: string;
  sales: {
    total_before_vat: number;
    vat_collected: number;
    total_with_vat: number;
    document_count: number;
    documents: DocumentSummary[];
  };
  purchases: {
    total_before_vat: number;
    vat_paid: number;
    total_with_vat: number;
    expense_count: number;
    expenses: ExpenseSummary[];
  };
  vat_liability: number;
  payment_due_date: string;
  export_urls: {
    pdf: string;
    excel: string;
    pcn874: string;
  }
}
ITA Integration
Submit to SHAAM (Internal)
TypeScript

POST /api/ita/submit-document
Request: {
  document_id: string;
}

Response: {
  success: boolean;
  allocation_number?: string;
  submission_date?: string;
  error?: string;
  queued_for_retry?: boolean;
}
Check ITA Status
TypeScript

GET /api/ita/status/{document_id}

Response: {
  status: 'pending' | 'approved' | 'failed';
  allocation_number?: string;
  last_attempt?: string;
  attempts: number;
  next_retry?: string;
}
Error Response Format
TypeScript

{
  error: {
    code: string;
    message: string;
    details?: any;
  }
}
Rate Limits
Authentication: 5 requests per 15 minutes
Document creation: 30 requests per minute
Reports: 10 requests per minute
Email scanning: 5 requests per hour
