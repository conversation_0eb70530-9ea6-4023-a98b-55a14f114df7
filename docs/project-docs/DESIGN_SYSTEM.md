# Design System - Israeli Invoice App

## 1. Design Principles

### Minimalist Aesthetic
- **Black and White Foundation**: The design system is built on a sophisticated grayscale palette that prioritizes clarity and professionalism
- **Content-First Approach**: Clean layouts that let content breathe and guide user attention naturally
- **Functional Beauty**: Every design element serves a purpose while maintaining visual elegance
- **Accessibility by Design**: High contrast ratios and clear visual hierarchy ensure usability for all users

### Hebrew-First Design
- **RTL-Native**: All components are designed with RTL (Right-to-Left) as the primary layout direction
- **Bilingual Harmony**: Seamless integration of Hebrew and English content with proper typography scaling
- **Cultural Sensitivity**: Design patterns that respect Hebrew reading patterns and cultural expectations

## 2. Design Tokens

### Colors

#### Core Palette (CSS Custom Properties)

**Dark Theme (Default)**
```css
--background: 0 0% 8%;           /* #141414 - Dark charcoal */
--foreground: 0 0% 95%;          /* #F2F2F2 - Light grey/white */
--card: 0 0% 12%;                /* #1F1F1F - Slightly lighter charcoal */
--card-foreground: 0 0% 95%;     /* #F2F2F2 - Light grey/white */
--primary: 0 0% 85%;             /* #D9D9D9 - Light grey */
--primary-foreground: 0 0% 15%;  /* #262626 - Dark charcoal */
--secondary: 0 0% 18%;           /* #2E2E2E - Medium charcoal */
--secondary-foreground: 0 0% 95%; /* #F2F2F2 - Light grey/white */
--muted: 0 0% 22%;               /* #383838 - Lighter charcoal */
--muted-foreground: 0 0% 70%;    /* #B3B3B3 - Medium grey */
--accent: 0 0% 70%;              /* #B3B3B3 - Light grey accent */
--accent-foreground: 0 0% 15%;   /* #262626 - Dark charcoal */
--border: 0 0% 22%;              /* #383838 - Medium charcoal border */
--input: 0 0% 22%;               /* #383838 - Input background */
--ring: 0 0% 70%;                /* #B3B3B3 - Focus ring */
```

**Light Theme**
```css
--background: 0 0% 98%;          /* #FAFAFA - Very light grey */
--foreground: 0 0% 20%;          /* #333333 - Dark grey */
--card: 0 0% 96%;                /* #F5F5F5 - Light grey card */
--card-foreground: 0 0% 20%;     /* #333333 - Dark grey */
--primary: 0 0% 25%;             /* #404040 - Dark grey primary */
--primary-foreground: 0 0% 98%;  /* #FAFAFA - Very light grey */
--secondary: 0 0% 92%;           /* #EBEBEB - Light grey secondary */
--secondary-foreground: 0 0% 25%; /* #404040 - Dark grey */
--muted: 0 0% 88%;               /* #E0E0E0 - Medium light grey */
--muted-foreground: 0 0% 45%;    /* #737373 - Medium grey */
--accent: 0 0% 30%;              /* #4D4D4D - Dark grey accent */
--accent-foreground: 0 0% 98%;   /* #FAFAFA - Very light grey */
--border: 0 0% 85%;              /* #D9D9D9 - Light grey border */
--input: 0 0% 90%;               /* #E6E6E6 - Input background */
--ring: 0 0% 30%;                /* #4D4D4D - Focus ring */
```

**Cosmic Accent Colors**
```css
--cosmic-dark: #404040;          /* Medium grey for light mode */
--cosmic-darker: #303030;        /* Darker grey for light mode */
--cosmic-light: #f0f0f0;         /* Light grey for light mode */
--cosmic-accent: #606060;        /* Medium-dark grey accent */
--cosmic-muted: #909090;         /* Medium grey */
```

**State Colors**
```css
--destructive: 0 84% 60%;        /* #F56565 - Error red */
--destructive-foreground: 210 40% 98%; /* #F7FAFC - Light text */
```

### Typography

#### Font Families
```css
font-family: {
  'inter': ['Inter', 'sans-serif'],           /* Primary Latin font */
  'secular': ['SecularOne', 'Inter', 'sans-serif'] /* Hebrew + fallback */
}
```

#### Font Weights
- **Light**: 300 - Used for large display text
- **Regular**: 400 - Body text and standard content
- **Medium**: 500 - Emphasized text and labels
- **Semibold**: 600 - Headings and important UI elements
- **Bold**: 700 - Strong emphasis and primary headings

#### Type Scale
```css
/* Heading Sizes */
.text-3xl: 1.875rem (30px)  /* Main page headings */
.text-4xl: 2.25rem (36px)   /* Hero headings */
.text-2xl: 1.5rem (24px)    /* Section headings */
.text-xl: 1.25rem (20px)    /* Card titles */
.text-lg: 1.125rem (18px)   /* Large body text */

/* Body Sizes */
.text-base: 1rem (16px)     /* Standard body text */
.text-sm: 0.875rem (14px)   /* Small text, captions */
.text-xs: 0.75rem (12px)    /* Micro text, badges */
```

#### Line Heights
- **Tight**: 1.25 - For headings and display text
- **Normal**: 1.5 - For body text
- **Relaxed**: 1.625 - For long-form content

#### Letter Spacing
```css
letter-spacing: {
  'tighter': '-0.05em',  /* Tight spacing for headings */
  'tight': '-0.025em',   /* Default body spacing */
  'normal': '0em',       /* Standard spacing */
}
```

### Spacing System

#### Base Scale (Tailwind)
```css
0: 0px
1: 0.25rem (4px)
2: 0.5rem (8px)
3: 0.75rem (12px)
4: 1rem (16px)
5: 1.25rem (20px)
6: 1.5rem (24px)
8: 2rem (32px)
10: 2.5rem (40px)
12: 3rem (48px)
16: 4rem (64px)
20: 5rem (80px)
24: 6rem (96px)
```

#### Component Spacing Patterns
- **Card Padding**: `p-6` (24px) for standard cards, `p-8` (32px) for pricing cards
- **Button Padding**: `px-4 py-2` (16px horizontal, 8px vertical)
- **Section Spacing**: `py-20` (80px) for major sections
- **Container Padding**: `px-6 md:px-12` (24px mobile, 48px desktop)

### Layout Grid

#### Container System
```css
.container {
  center: true,
  padding: '2rem',
  screens: {
    '2xl': '1400px'  /* Maximum container width */
  }
}
```

#### Responsive Breakpoints
```css
sm: '640px'   /* Small devices */
md: '768px'   /* Medium devices */
lg: '1024px'  /* Large devices */
xl: '1280px'  /* Extra large devices */
2xl: '1536px' /* 2X large devices */
```

#### Grid Patterns
- **3-Column Layout**: `grid-cols-1 md:grid-cols-3` (pricing, features)
- **5-Column Layout**: `grid-cols-1 md:grid-cols-5` (footer)
- **Responsive Flex**: `flex-col sm:flex-row` (hero buttons)

### Elevation (Shadows)

#### Shadow Scale
```css
.shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05)     /* Subtle elevation */
.shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1)         /* Standard cards */
.shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1)   /* Hover states */
.shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1) /* Prominent elements */
```

#### Custom Glows
```css
.cosmic-glow:hover {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2); /* Dark theme */
}

.light .cosmic-glow:hover {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); /* Light theme */
}

.pricing-glow {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.15); /* Pricing highlight */
}
```

### Border Radius

#### Radius Scale
```css
--radius: 0.5rem (8px)  /* Base radius */

.rounded-md: 0.375rem (6px)    /* Small components */
.rounded-lg: 0.5rem (8px)      /* Cards, inputs */
.rounded-xl: 0.75rem (12px)    /* Large cards */
.rounded-2xl: 1rem (16px)      /* Mobile menu */
.rounded-full: 9999px          /* Pills, badges */
```

### Animation

#### Transition Durations
```css
.transition-colors: 150ms     /* Color changes */
.transition-all: 150ms        /* General transitions */
.duration-200: 200ms          /* Button interactions */
.duration-300: 300ms          /* Card hovers, cosmic-glow */
```

#### Custom Animations
```css
@keyframes accordion-down {
  from { height: 0 }
  to { height: var(--radix-accordion-content-height) }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height) }
  to { height: 0 }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) }
  50% { transform: translateY(-10px) }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 1 }
  50% { opacity: 0.5 }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50% }
  50% { background-position: 100% 50% }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px) }
  to { opacity: 1; transform: translateY(0) }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px) }
  to { opacity: 1; transform: translateY(0) }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px) }
  to { opacity: 1; transform: translateY(0) }
}
```

#### Animation Classes
```css
.animate-accordion-down: accordion-down 0.2s ease-out
.animate-accordion-up: accordion-up 0.2s ease-out
.animate-float: float 6s ease-in-out infinite
.animate-pulse-slow: pulse-slow 4s ease-in-out infinite
.animate-gradient-shift: gradient-shift 8s ease infinite
.animate-fadeIn: fadeIn 0.6s ease-out
.animate-slideDown: slideDown 0.4s ease-out
.animate-slideUp: slideUp 0.6s ease-out
```

## 3. Component Library

### Buttons

#### Button Variants
```tsx
// Default Button
<Button variant="default" size="default">
  Primary Action
</Button>

// Variant Classes:
variant="default"     // bg-primary text-primary-foreground hover:bg-primary-foreground hover:text-primary
variant="destructive" // bg-destructive text-destructive-foreground hover:bg-destructive/90
variant="outline"     // border border-input bg-background hover:bg-accent hover:text-accent-foreground
variant="secondary"   // bg-secondary text-secondary-foreground hover:bg-secondary/80
variant="ghost"       // hover:bg-accent hover:text-accent-foreground
variant="link"        // text-primary underline-offset-4 hover:underline
```

#### Button Sizes
```tsx
size="default"  // h-10 px-4 py-2
size="sm"       // h-9 rounded-md px-3
size="lg"       // h-11 rounded-md px-8
size="icon"     // h-10 w-10
```

#### Button States
- **Default**: Clean, minimal styling with subtle borders
- **Hover**: Inverted colors with smooth transition (200ms)
- **Focus**: Ring outline with `focus-visible:ring-2 focus-visible:ring-ring`
- **Disabled**: `disabled:pointer-events-none disabled:opacity-50`

#### Custom Button Patterns
```tsx
// Hero Section Buttons
<Button className="bg-primary text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground text-base h-12 px-8 transition-all duration-200 min-h-[48px]">
  תכונות
</Button>

// Pricing Card Buttons
<Button className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
  {plan.buttonText}
</Button>
```

### Cards

#### Card Structure
```tsx
<Card>           // rounded-lg border bg-card text-card-foreground shadow-sm
  <CardHeader>   // flex flex-col space-y-1.5 p-6
    <CardTitle>  // text-2xl font-semibold leading-none tracking-tight
    <CardDescription> // text-sm text-muted-foreground
  </CardHeader>
  <CardContent>  // p-6 pt-0
  <CardFooter>   // flex items-center p-6 pt-0
</Card>
```

#### Card Variants

**Standard Card**
```css
.card {
  @apply rounded-lg border bg-card text-card-foreground shadow-sm;
}
```

**Cosmic Card**
```css
.cosmic-card {
  @apply bg-card border border-border backdrop-blur-sm;
}
```

**Glass Card**
```css
.cosmic-glass {
  @apply backdrop-blur-sm bg-card border border-white/10 shadow-[0_0_15px_rgba(255,255,255,0.1)];
}

.light .cosmic-glass {
  @apply backdrop-blur-sm bg-card border border-black/10 shadow-[0_0_15px_rgba(0,0,0,0.05)];
}
```

**Pricing Card with Glow**
```css
.pricing-glow {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.15);
  transition: box-shadow 0.3s ease;
}

.pricing-glow:hover {
  box-shadow: 0 0 30px hsl(var(--primary) / 0.25);
}
```

### Forms & Inputs

#### Input Component
```tsx
<Input
  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-right"
  dir="rtl"
/>
```

#### Password Input with Toggle
```tsx
<PasswordInput
  className="pr-10 text-right"
  dir="rtl"
  error={hasError}
/>
```

#### Input States
- **Default**: Clean border with subtle background
- **Focus**: Ring outline with smooth transition
- **Error**: `border-destructive` for validation errors
- **Disabled**: Reduced opacity and no pointer events
- **RTL**: `text-right` and `dir="rtl"` for Hebrew support

### Badges

#### Badge Variants
```tsx
variant="default"     // border-transparent bg-primary text-primary-foreground hover:bg-primary/80
variant="secondary"   // border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80
variant="destructive" // border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80
variant="outline"     // text-foreground
```

#### Badge Styling
```css
.badge {
  @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}
```

### Navigation

#### Navigation Pills
```css
.nav-pill {
  @apply backdrop-blur-md bg-card border border-border rounded-full;
}

.nav-pill-item {
  @apply px-4 py-2 text-sm rounded-full transition-colors;
}

.nav-pill-item:hover {
  @apply bg-accent text-accent-foreground;
}

.nav-pill-item.active {
  @apply bg-accent text-accent-foreground;
}
```

#### Header Navigation Pattern
```tsx
<nav className="hidden md:flex items-center absolute right-1/2 transform translate-x-1/2">
  <div className="rounded-full px-1 py-1 backdrop-blur-md bg-background/80 border border-border shadow-lg">
    <ToggleGroup type="single" value={activePage}>
      <ToggleGroupItem value="features" className="px-4 py-2 rounded-full transition-colors">
        <FileText size={16} className="inline-block ml-1.5" /> תכונות
      </ToggleGroupItem>
    </ToggleGroup>
  </div>
</nav>
```

#### Mobile Navigation
```tsx
<div className="md:hidden absolute top-20 right-4 left-4 bg-background/95 backdrop-blur-md py-4 px-6 border border-border rounded-2xl shadow-lg z-50">
  <div className="flex flex-col gap-4">
    {/* Navigation items */}
  </div>
</div>
```

### Task Cards (Interactive Components)

#### Task Card Base
```css
.task-card {
  @apply transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg hover:border-primary/30;
}

.task-card.dragging {
  @apply scale-105 shadow-lg border-primary opacity-70 rotate-1 z-10;
}

.task-card.dragged-over {
  @apply border-primary border-dashed;
}
```

#### Task Card Animations
```css
.task-card-enter {
  @apply opacity-0 -translate-y-4;
}

.task-card-enter-active {
  @apply opacity-100 translate-y-0 transition-all duration-300 ease-out;
}

.task-card-exit {
  @apply opacity-100;
}

.task-card-exit-active {
  @apply opacity-0 translate-y-4 transition-all duration-300 ease-in;
}
```

## 4. Layout Patterns

### Page Structure
```tsx
<div className="min-h-screen flex flex-col bg-background text-foreground" dir="rtl">
  <Header />
  <main>
    <HeroSection />
    <FeaturesSection />
    <Pricing />
    <CalendarSection />
  </main>
  <Footer />
</div>
```

### Container Patterns
```tsx
// Standard Section Container
<section className="w-full py-20 px-6 md:px-12 bg-background">
  <div className="max-w-7xl mx-auto space-y-16">
    {/* Content */}
  </div>
</section>

// Header Container
<header className="w-full max-w-7xl mx-auto py-3 px-6 md:px-8 flex items-center justify-between">
  {/* Header content */}
</header>
```

### Grid Layouts

#### Pricing Grid
```tsx
<div className="grid grid-cols-1 md:grid-cols-3 gap-8">
  {pricingPlans.map((plan, index) => (
    <PricingCard key={index} plan={plan} />
  ))}
</div>
```

#### Footer Grid
```tsx
<div className="grid grid-cols-1 md:grid-cols-5 gap-10">
  <div className="md:col-span-2 space-y-6">
    {/* Logo and description */}
  </div>
  <div className="space-y-4">
    {/* Footer columns */}
  </div>
</div>
```

### Responsive Patterns

#### Flex Direction Changes
```css
.flex-col.sm:flex-row  /* Stack on mobile, row on desktop */
.hidden.md:flex        /* Hide on mobile, show on desktop */
.md:hidden             /* Show on mobile, hide on desktop */
```

#### Text Size Scaling
```css
.text-3xl.md:text-4xl  /* Smaller on mobile, larger on desktop */
.text-lg.md:text-xl    /* Progressive scaling */
```

#### Spacing Adjustments
```css
.px-6.md:px-12         /* 24px mobile, 48px desktop */
.py-16.md:py-20        /* 64px mobile, 80px desktop */
```

## 5. Interaction Patterns

### Hover Effects

#### Cosmic Glow
```css
.cosmic-glow {
  position: relative;
  transition: all 0.3s ease;
}

.cosmic-glow:hover {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.light .cosmic-glow:hover {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
}
```

#### Icon Glow
```css
.icon-glow {
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
  transition: filter 0.3s ease;
}

.light .icon-glow {
  filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.2));
}

.collapsible-trigger:hover .icon-glow {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.4));
}
```

### Focus States

#### Ring Focus
```css
focus-visible:outline-none
focus-visible:ring-2
focus-visible:ring-ring
focus-visible:ring-offset-2
```

#### Button Focus
```css
.button:focus-visible {
  @apply ring-2 ring-ring ring-offset-2;
}
```

### Loading States

#### Skeleton Pattern
```tsx
<div className="animate-pulse">
  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
  <div className="h-4 bg-muted rounded w-1/2"></div>
</div>
```

### Micro-interactions

#### Button Press
```css
.button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
```

#### Card Hover
```css
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
```

## 6. Accessibility Guidelines

### Color Contrast

#### WCAG AA Compliance
- **Normal Text**: Minimum 4.5:1 contrast ratio
- **Large Text**: Minimum 3:1 contrast ratio
- **UI Components**: Minimum 3:1 contrast ratio

#### High Contrast Pairs
```css
/* Dark Theme */
--foreground: 0 0% 95%;    /* #F2F2F2 on */
--background: 0 0% 8%;     /* #141414 = 11.9:1 ratio ✓ */

/* Light Theme */
--foreground: 0 0% 20%;    /* #333333 on */
--background: 0 0% 98%;    /* #FAFAFA = 12.6:1 ratio ✓ */
```

### Focus Indicators

#### Keyboard Navigation
```css
/* Always visible focus rings */
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
```

#### Focus Order
- Logical tab order following RTL reading pattern
- Skip links for main content navigation
- Proper focus trapping in modals and dropdowns

### ARIA Labels

#### Screen Reader Support
```tsx
// Button with icon
<Button aria-label="Close menu">
  <X size={24} />
</Button>

// Navigation landmarks
<nav aria-label="Main navigation">
<main aria-label="Main content">
<footer aria-label="Site footer">
```

#### Form Labels
```tsx
<Label htmlFor="email">כתובת אימייל</Label>
<Input id="email" type="email" aria-describedby="email-error" />
<span id="email-error" role="alert">שגיאה בכתובת האימייל</span>
```

## 7. RTL Support

### Global RTL Configuration

#### HTML Direction
```html
<html dir="rtl" lang="he">
```

#### CSS RTL Foundation
```css
/* Global RTL support */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
  text-align: right;
  direction: rtl;
}
```

#### Tailwind RTL Variants
```css
/* Custom RTL variants in tailwind.config.ts */
addVariant('rtl', '[dir="rtl"] &')
addVariant('ltr', '[dir="ltr"] &')
```

### Component RTL Patterns

#### Input Components
```tsx
<Input
  className="text-right"
  dir="rtl"
/>

<PasswordInput
  className="pr-10 text-right"  // Icon on left for RTL
  dir="rtl"
/>
```

#### Navigation RTL
```tsx
// Header positioned for RTL
<nav className="hidden md:flex items-center absolute right-1/2 transform translate-x-1/2">

// Mobile menu from right side
<Sheet side="right">
```

#### Icon Positioning
```tsx
// Icons positioned for RTL reading
<FileText size={16} className="inline-block ml-1.5" /> תכונות
// ml-1.5 creates space to the left of text in RTL
```

### Hebrew Typography

#### Font Selection
```css
font-family: 'SecularOne', 'Inter', 'sans-serif'
```

#### Text Alignment
```css
.text-right  /* Default for Hebrew text */
.text-center /* For centered content */
```

#### Letter Spacing
```css
letter-spacing: -0.025em  /* Optimized for Hebrew */
```

## 8. Code Examples

### CSS Variables Usage
```css
:root {
  /* Core color system */
  --background: 0 0% 8%;
  --foreground: 0 0% 95%;
  --primary: 0 0% 85%;
  --primary-foreground: 0 0% 15%;

  /* Spacing system */
  --radius: 0.5rem;

  /* Custom cosmic colors */
  --cosmic-dark: #404040;
  --cosmic-accent: #606060;
}

/* Usage in components */
.custom-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-radius: var(--radius);
}
```

### Utility Classes
```css
/* Cosmic-themed utilities */
.cosmic-gradient {
  background: linear-gradient(135deg, rgba(64, 64, 64, 0.2) 0%, rgba(32, 32, 32, 0.4) 100%);
}

.cosmic-grid {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
}

.text-balance {
  text-wrap: balance;
}
```

### Component Class Patterns
```tsx
// Button with cosmic styling
<Button className="bg-primary text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground text-base h-12 px-8 transition-all duration-200 min-h-[48px]">

// Card with glow effect
<div className="p-8 rounded-xl border border-primary/50 bg-card pricing-glow">

// Navigation pill
<div className="rounded-full px-1 py-1 backdrop-blur-md bg-background/80 border border-border shadow-lg">

// Glass effect
<div className="cosmic-glass backdrop-blur-sm bg-card border border-white/10 shadow-[0_0_15px_rgba(255,255,255,0.1)]">
```

## 9. Usage Guidelines

### Do's and Don'ts

#### Colors
✅ **DO:**
- Use CSS custom properties for all color references
- Maintain high contrast ratios (4.5:1 minimum)
- Test in both light and dark themes
- Use the cosmic accent colors for subtle highlights

❌ **DON'T:**
- Hardcode color values in components
- Use colors that fail accessibility standards
- Mix color systems (stick to the defined palette)
- Ignore theme variations

#### Typography
✅ **DO:**
- Use SecularOne for Hebrew text
- Apply proper letter spacing (-0.025em)
- Use the defined type scale
- Test with actual Hebrew content

❌ **DON'T:**
- Mix font families within components
- Use arbitrary font sizes
- Ignore RTL text alignment
- Use Lorem Ipsum for Hebrew testing

#### Spacing
✅ **DO:**
- Use the 8px base grid system
- Apply consistent padding patterns
- Use gap utilities for flex/grid layouts
- Follow the defined spacing scale

❌ **DON'T:**
- Use arbitrary spacing values
- Mix margin and padding inconsistently
- Ignore responsive spacing patterns
- Break the visual rhythm

#### Components
✅ **DO:**
- Extend existing component variants
- Follow established naming conventions
- Include proper accessibility attributes
- Test RTL layout behavior

❌ **DON'T:**
- Create one-off component styles
- Ignore existing component patterns
- Skip accessibility considerations
- Assume LTR-only usage

### Implementation Notes

#### Theme Integration
```tsx
// Always wrap app with ThemeProvider
<ThemeProvider attribute="class" defaultTheme="system" enableSystem>
  <App />
</ThemeProvider>
```

#### RTL Integration
```tsx
// Set direction on root container
<div className="min-h-screen flex flex-col bg-background text-foreground" dir="rtl">
```

#### Component Composition
```tsx
// Prefer composition over customization
<Card className="cosmic-glow">
  <CardHeader>
    <CardTitle>כותרת</CardTitle>
    <CardDescription>תיאור</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Content */}
  </CardContent>
</Card>
```

### Browser Compatibility

#### Supported Browsers
- **Chrome**: 90+ (full support)
- **Firefox**: 88+ (full support)
- **Safari**: 14+ (full support)
- **Edge**: 90+ (full support)

#### CSS Features Used
- CSS Custom Properties (CSS Variables)
- CSS Grid and Flexbox
- CSS Transforms and Transitions
- CSS Backdrop Filter (with fallbacks)
- CSS Text Balance (progressive enhancement)

#### Fallback Strategies
```css
/* Backdrop filter fallback */
.cosmic-glass {
  background-color: hsl(var(--card));
  backdrop-filter: blur(8px);
}

@supports not (backdrop-filter: blur(8px)) {
  .cosmic-glass {
    background-color: hsl(var(--card) / 0.95);
  }
}
```

## 10. Maintenance and Evolution

### Design Token Updates
- All color changes should be made in CSS custom properties
- Test changes in both light and dark themes
- Verify accessibility compliance after updates
- Document any breaking changes

### Component Additions
- Follow existing naming conventions
- Include all necessary variants and states
- Add proper TypeScript types
- Document usage examples

### Performance Considerations
- Minimize CSS bundle size through tree-shaking
- Use CSS custom properties for dynamic theming
- Optimize animation performance with transform/opacity
- Lazy load non-critical components

---

## Status: ✅ COMPLETE AND VALIDATED

This design system serves as the single source of truth for all UI development in the Israeli Invoice Application. Every component built should reference this document to maintain perfect consistency with the landing page design.

**Last Updated**: 2025-01-29
**Version**: 1.0.0
**Maintainer**: Development Team
