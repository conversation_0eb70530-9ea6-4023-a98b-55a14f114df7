# Israeli B2B Invoicing Application - Database Schema

## Overview
This document describes the complete database schema for the Israeli B2B invoicing application built on Supabase PostgreSQL.

## Architecture Principles
- **Multi-tenant**: Company-based isolation using Row Level Security (RLS)
- **Audit Trail**: Comprehensive logging of all actions
- **Israeli Law Compliance**: Sequential numbering, VAT handling, 7-year retention
- **Security**: Row Level Security on all tables with role-based access

## Database Tables

### Core Tables

#### 1. Companies
Multi-tenant root table for company isolation.
```sql
- id (UUID, PK)
- name (VARCHAR(255), NOT NULL)
- business_number (VARCHAR(50), UNIQUE) -- Israeli business registration
- vat_number (VARCHAR(20), UNIQUE) -- Israeli VAT number
- address, city, postal_code, phone, email, website
- logo_url (TEXT)
- settings (JSONB)
- is_active (BOOLEAN, DEFAULT true)
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

#### 2. Users
Extends Supabase auth.users with role-based access.
```sql
- id (UUID, PK, FK to auth.users)
- company_id (UUID, FK to companies)
- role (user_role ENUM: company_admin, company_user, accountant)
- first_name, last_name, phone (VARCHAR)
- avatar_url (TEXT)
- is_active (BOOLEAN, DEFAULT true)
- last_login_at, created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

#### 3. Customers
Client information with company isolation.
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies, NOT NULL)
- name (VARCHAR(255), NOT NULL)
- business_number, vat_number (VARCHAR)
- contact_person, email, phone (VARCHAR)
- address, city, postal_code (VARCHAR)
- payment_terms (INTEGER, DEFAULT 30) -- Days
- credit_limit (DECIMAL(15,2), DEFAULT 0)
- notes (TEXT)
- is_active (BOOLEAN, DEFAULT true)
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

#### 4. Products
Items and services that can be invoiced.
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies, NOT NULL)
- name (VARCHAR(255), NOT NULL)
- description (TEXT)
- sku (VARCHAR(100)) -- Stock Keeping Unit
- unit_price (DECIMAL(15,2), NOT NULL, DEFAULT 0)
- unit_of_measure (VARCHAR(50), DEFAULT 'unit')
- vat_rate (DECIMAL(5,2), DEFAULT 18.00) -- Israeli standard VAT
- category (VARCHAR(100))
- is_service (BOOLEAN, DEFAULT false)
- is_active (BOOLEAN, DEFAULT true)
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

### Document Management

#### 5. Document Sequences
Sequential numbering per document type (Israeli law compliance).
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies, NOT NULL)
- document_type (document_type ENUM)
- year (INTEGER, NOT NULL)
- current_number (INTEGER, NOT NULL, DEFAULT 0)
- prefix, suffix (VARCHAR(10))
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
- UNIQUE(company_id, document_type, year)
```

#### 6. Documents
Main invoices, receipts, and credit notes.
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies, NOT NULL)
- customer_id (UUID, FK to customers, NOT NULL)
- created_by (UUID, FK to users, NOT NULL)
- approved_by (UUID, FK to users)
- document_type (document_type ENUM: tax_invoice, receipt, tax_invoice_receipt, credit_note)
- document_number (VARCHAR(50), NOT NULL)
- status (document_status ENUM: draft, sent, paid, cancelled, overdue)
- issue_date, due_date, payment_date (DATE)
- subtotal, vat_amount, total_amount, paid_amount (DECIMAL(15,2))
- ita_allocation_number (VARCHAR(50)) -- ITA SHAAM system
- ita_submitted_at (TIMESTAMP WITH TIME ZONE)
- notes, terms (TEXT)
- pdf_url (TEXT)
- attachments (JSONB, DEFAULT '[]')
- currency (VARCHAR(3), DEFAULT 'ILS')
- exchange_rate (DECIMAL(10,4), DEFAULT 1.0000)
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

#### 7. Document Items
Line items for documents.
```sql
- id (UUID, PK)
- document_id (UUID, FK to documents, NOT NULL)
- product_id (UUID, FK to products)
- description (TEXT, NOT NULL)
- quantity (DECIMAL(10,3), NOT NULL, DEFAULT 1)
- unit_price (DECIMAL(15,2), NOT NULL)
- discount_percent, discount_amount (DECIMAL)
- line_total, vat_amount, total_amount (DECIMAL(15,2), NOT NULL)
- vat_rate (DECIMAL(5,2), NOT NULL, DEFAULT 18.00)
- unit_of_measure (VARCHAR(50), DEFAULT 'unit')
- sort_order (INTEGER, DEFAULT 0)
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

### System Tables

#### 8. Audit Logs
Comprehensive tracking of all actions.
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies)
- user_id (UUID, FK to users)
- action (audit_action ENUM: create, update, delete, view, approve, reject)
- table_name (VARCHAR(100), NOT NULL)
- record_id (UUID)
- old_values, new_values (JSONB)
- changed_fields (TEXT[])
- ip_address (INET)
- user_agent, session_id (TEXT)
- description (TEXT)
- created_at (TIMESTAMP WITH TIME ZONE, DEFAULT NOW())
```

#### 9. Company Settings
Company-specific configurations.
```sql
- id (UUID, PK)
- company_id (UUID, FK to companies, NOT NULL, UNIQUE)
- default_payment_terms (INTEGER, DEFAULT 30)
- default_vat_rate (DECIMAL(5,2), DEFAULT 18.00)
- invoice_template (VARCHAR(50), DEFAULT 'standard')
- invoice_footer (TEXT)
- invoice_prefix, receipt_prefix, credit_note_prefix (VARCHAR(10))
- email_signature (TEXT)
- auto_send_invoices (BOOLEAN, DEFAULT false)
- notify_overdue_days (INTEGER, DEFAULT 7)
- notify_payment_received (BOOLEAN, DEFAULT true)
- ita_api_enabled (BOOLEAN, DEFAULT false)
- ita_api_credentials (JSONB, DEFAULT '{}')
- created_at, updated_at (TIMESTAMP WITH TIME ZONE)
```

## Custom Types (ENUMs)

```sql
-- User roles
CREATE TYPE user_role AS ENUM ('company_admin', 'company_user', 'accountant');

-- Document types (Israeli law compliance)
CREATE TYPE document_type AS ENUM ('tax_invoice', 'receipt', 'tax_invoice_receipt', 'credit_note');

-- Document status
CREATE TYPE document_status AS ENUM ('draft', 'sent', 'paid', 'cancelled', 'overdue');

-- Audit actions
CREATE TYPE audit_action AS ENUM ('create', 'update', 'delete', 'view', 'approve', 'reject');
```

## Views

### Document Summary
Combines document and customer information for easy querying.
```sql
CREATE VIEW document_summary AS
SELECT 
    d.*, 
    c.name AS customer_name,
    c.email AS customer_email,
    u.first_name || ' ' || u.last_name AS created_by_name,
    CASE WHEN d.due_date < CURRENT_DATE AND d.status NOT IN ('paid', 'cancelled') 
         THEN true ELSE false END AS is_overdue
FROM documents d
JOIN customers c ON d.customer_id = c.id
JOIN users u ON d.created_by = u.id;
```

### Company Statistics
Aggregated statistics per company.
```sql
CREATE VIEW company_statistics AS
SELECT 
    co.id AS company_id,
    co.name AS company_name,
    COUNT(DISTINCT cu.id) AS total_customers,
    COUNT(DISTINCT p.id) AS total_products,
    COUNT(DISTINCT d.id) AS total_documents,
    SUM(d.total_amount) AS total_revenue,
    SUM(CASE WHEN d.status = 'paid' THEN d.total_amount ELSE 0 END) AS paid_revenue,
    SUM(CASE WHEN d.status NOT IN ('paid', 'cancelled') THEN d.total_amount - d.paid_amount ELSE 0 END) AS outstanding_amount
FROM companies co
LEFT JOIN customers cu ON co.id = cu.company_id
LEFT JOIN products p ON co.id = p.company_id  
LEFT JOIN documents d ON co.id = d.company_id
GROUP BY co.id, co.name;
```

## Functions

### Document Number Generation
```sql
-- Get next sequential document number (Israeli law compliance)
CREATE FUNCTION get_next_document_number(
    p_company_id UUID,
    p_document_type document_type,
    p_year INTEGER DEFAULT EXTRACT(YEAR FROM NOW())
) RETURNS TEXT
```

### Document Creation
```sql
-- Create new document with automatic numbering
CREATE FUNCTION create_document(
    p_company_id UUID,
    p_customer_id UUID,
    p_document_type document_type,
    p_issue_date DATE DEFAULT CURRENT_DATE,
    p_due_date DATE DEFAULT NULL
) RETURNS UUID
```

### Status Management
```sql
-- Update document status
CREATE FUNCTION update_document_status(
    p_document_id UUID,
    p_status document_status
) RETURNS BOOLEAN
```

## Triggers

### Automatic Calculations
- **Document Item Totals**: Automatically calculates line_total, vat_amount, total_amount
- **Document Totals**: Updates document totals when items change
- **Updated At**: Automatically updates updated_at timestamps

### Audit Logging
- **Comprehensive Tracking**: All table changes are automatically logged to audit_logs
- **Change Detection**: Tracks old/new values and changed fields
- **User Context**: Records user_id, IP address, session information

## Row Level Security (RLS)

All tables have RLS enabled with policies for:

### Multi-tenant Isolation
- Users can only access data from their own company
- Company admins have full access to their company data
- Accountants have read access to all companies

### Role-based Access
- **Company Admin**: Full CRUD access to company data
- **Company User**: Limited access based on business rules
- **Accountant**: Read access + document approval capabilities

## Indexes

Performance indexes are created on:
- Foreign key relationships
- Frequently queried fields (company_id, document_type, status)
- Date fields for reporting
- Text fields for search functionality

## Security Features

1. **Row Level Security**: Multi-tenant data isolation
2. **Audit Trail**: Complete change tracking
3. **Role-based Access**: Granular permissions
4. **Data Validation**: Check constraints and triggers
5. **Sequential Numbering**: Gap-free document numbering
6. **Encryption**: Supabase handles encryption at rest and in transit

## Compliance

### Israeli Law Requirements
- ✅ Sequential document numbering (no gaps)
- ✅ 18% VAT rate support
- ✅ Document type compliance (Tax Invoice, Receipt, etc.)
- ✅ 7-year retention capability
- ✅ ITA SHAAM integration fields
- ✅ Bilingual support ready

### Data Protection
- ✅ GDPR compliant structure
- ✅ Israeli Privacy Law ready
- ✅ Audit trail for compliance
- ✅ Data retention policies

---

**Status**: ✅ COMPLETE AND DEPLOYED
**Last Updated**: 2025-01-29
**Version**: 1.0.0
