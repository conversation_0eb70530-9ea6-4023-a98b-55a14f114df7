{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "outDir": "dist", "rootDir": "src"}, "include": ["src"], "exclude": ["dist", "node_modules"]}