// Shared TypeScript types and interfaces

// User types
export interface User {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  companyName?: string;
  annualRevenue?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Company types
export interface Company {
  id: string;
  name: string;
  annualRevenue: string;
  ownerId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Survey types
export interface SignupSurvey {
  companyName: string;
  annualRevenue: string;
  fullName: string;
  email: string;
  phone: string;
  accountingServicesInterest: boolean;
  businessLoanInterest: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Common utility types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
