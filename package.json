{"name": "fintech-app-monorepo", "private": true, "version": "1.0.0", "description": "Fintech application monorepo with landing page and web app", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "npm run dev --workspace=apps/landing", "dev:landing": "npm run dev --workspace=apps/landing", "dev:web": "npm run dev --workspace=apps/web", "build": "npm run build --workspaces", "build:landing": "npm run build --workspace=apps/landing", "build:web": "npm run build --workspace=apps/web", "lint": "npm run lint --workspaces", "test": "npm run test --workspaces", "clean": "rm -rf node_modules apps/*/node_modules packages/*/node_modules apps/*/dist packages/*/dist"}, "devDependencies": {"@types/node": "^22.5.5", "typescript": "^5.5.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}