# Supabase configuration file

project_id = "zhwqtgypoueykwgphmqn"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
site_url = "https://zhwqtgypoueykwgphmqn.supabase.co"
additional_redirect_urls = ["http://localhost:3000", "https://localhost:3000", "https://zhwqtgypoueykwgphmqn.supabase.co/auth/v1/verify"]
jwt_expiry = 3600

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true

[auth.sms]
enable_signup = false

[auth.external.apple]
enabled = false

[auth.external.azure]
enabled = false

[auth.external.bitbucket]
enabled = false

[auth.external.discord]
enabled = false

[auth.external.facebook]
enabled = false

[auth.external.github]
enabled = false

[auth.external.gitlab]
enabled = false

[auth.external.google]
enabled = false

[auth.external.keycloak]
enabled = false

[auth.external.linkedin]
enabled = false

[auth.external.notion]
enabled = false

[auth.external.twitch]
enabled = false

[auth.external.twitter]
enabled = false

[auth.external.slack]
enabled = false

[auth.external.spotify]
enabled = false

[auth.external.workos]
enabled = false

[auth.external.zoom]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "IPv4"

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
file_size_limit = "50MiB"
image_transformation = { enabled = true }

[analytics]
enabled = false
port = 54327
vector_port = 54328
gcp_project_id = ""
gcp_project_number = ""
gcp_jwt_path = "supabase/gcp.json"

[functions]

[functions.auth-register]
verify_jwt = false

[functions.auth-login]
verify_jwt = false

[functions.documents-next-number]
verify_jwt = true

[functions.documents-create]
verify_jwt = true

[functions.documents-send]
verify_jwt = true

[functions.customers-create]
verify_jwt = true

[functions.customers-search]
verify_jwt = true

[functions.expenses-update-status]
verify_jwt = true

[functions.reports-vat]
verify_jwt = true

[functions.ita-submit-document]
verify_jwt = true

[functions.process-ita-queue]
verify_jwt = false

[functions.generate-pdf]
verify_jwt = true

[functions.scan-emails]
verify_jwt = false

[functions.push-token-register]
verify_jwt = true

[functions.send-push-notification]
verify_jwt = false

[functions.auth-verify]
verify_jwt = false
