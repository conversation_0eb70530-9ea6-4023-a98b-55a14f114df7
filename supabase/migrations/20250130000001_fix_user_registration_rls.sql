-- Fix RLS policies to allow user registration
-- This migration adds the missing INSERT policies for user registration

-- Enable RLS on all tables (if not already enabled)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;

-- Users table policies
-- Allow users to insert their own record during registration
CREATE POLICY "Users can create their own account" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow users to view their own record
CREATE POLICY "Users can view their own record" ON users
    FOR SELECT USING (auth.uid() = id);

-- Allow users to update their own record
CREATE POLICY "Users can update their own record" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Companies table policies
-- Allow authenticated users to create companies (for registration)
CREATE POLICY "Authenticated users can create companies" ON companies
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow users to view companies they belong to
CREATE POLICY "Users can view their companies" ON companies
    FOR SELECT USING (
        id IN (
            SELECT company_id FROM company_users 
            WHERE user_id = auth.uid()
        )
    );

-- Allow company admins to update their company
CREATE POLICY "Company admins can update company" ON companies
    FOR UPDATE USING (
        id IN (
            SELECT company_id FROM company_users 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Company_users table policies
-- Allow authenticated users to create company-user relationships (for registration)
CREATE POLICY "Authenticated users can create company relationships" ON company_users
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND 
        (user_id = auth.uid() OR created_by = auth.uid())
    );

-- Allow users to view their company relationships
CREATE POLICY "Users can view their company relationships" ON company_users
    FOR SELECT USING (user_id = auth.uid());

-- Allow company admins to manage company relationships
CREATE POLICY "Company admins can manage relationships" ON company_users
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM company_users 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Super admin policies (keep existing functionality)
CREATE POLICY "Super admins can manage all users" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'super_admin'
        )
    );

CREATE POLICY "Super admins can manage all companies" ON companies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'super_admin'
        )
    );

CREATE POLICY "Super admins can manage all company relationships" ON company_users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'super_admin'
        )
    );

-- Add helpful functions for role checking
CREATE OR REPLACE FUNCTION is_company_admin(user_id UUID, company_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM company_users 
        WHERE company_users.user_id = is_company_admin.user_id 
        AND company_users.company_id = is_company_admin.company_id 
        AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_companies(user_id UUID)
RETURNS TABLE(company_id UUID) AS $$
BEGIN
    RETURN QUERY
    SELECT cu.company_id 
    FROM company_users cu 
    WHERE cu.user_id = get_user_companies.user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
