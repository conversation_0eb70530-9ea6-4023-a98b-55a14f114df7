// Configuration for Supabase Edge Functions

export const config = {
  // Rate limiting configuration
  rateLimits: {
    'auth-register': { window: 15 * 60 * 1000, max: 5 }, // 5 requests per 15 minutes
    'auth-login': { window: 15 * 60 * 1000, max: 10 }, // 10 requests per 15 minutes
    'documents-create': { window: 60 * 1000, max: 30 }, // 30 requests per minute
    'documents-send': { window: 60 * 1000, max: 20 }, // 20 requests per minute
    'customers-create': { window: 60 * 1000, max: 50 }, // 50 requests per minute
    'customers-search': { window: 60 * 1000, max: 100 }, // 100 requests per minute
    'expenses-update-status': { window: 60 * 1000, max: 50 }, // 50 requests per minute
    'reports-vat': { window: 60 * 1000, max: 10 }, // 10 requests per minute
  },

  // Validation rules
  validation: {
    businessNumber: /^[0-9]{9}$/,
    vatId: /^[0-9]{9}$/,
    israeliPhone: /^(\+972|0)(5[0-9]|7[1-9])[0-9]{7}$/,
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    password: /^(?=.*[A-Z])(?=.*\d).{8,}$/,
    amount: /^\d+(\.\d{1,2})?$/,
    date: /^\d{4}-\d{2}-\d{2}$/,
  },

  // Document types
  documentTypes: {
    TAX_INVOICE: 'tax_invoice',
    RECEIPT: 'receipt',
    CREDIT_NOTE: 'credit_note',
    TAX_INVOICE_RECEIPT: 'tax_invoice_receipt',
  },

  // Document statuses
  documentStatuses: {
    DRAFT: 'draft',
    PENDING_ALLOCATION: 'pending_allocation',
    APPROVED: 'approved',
    SENT: 'sent',
    PAID: 'paid',
    CANCELLED: 'cancelled',
  },

  // Expense categories
  expenseCategories: {
    OFFICE_SUPPLIES: 'office_supplies',
    TRAVEL: 'travel',
    UTILITIES: 'utilities',
    RENT: 'rent',
    PROFESSIONAL_SERVICES: 'professional_services',
    MARKETING: 'marketing',
    EQUIPMENT: 'equipment',
    OTHER: 'other',
  },

  // User roles
  userRoles: {
    ADMIN: 'admin',
    USER: 'user',
    ACCOUNTANT: 'accountant',
  },

  // Default values
  defaults: {
    currency: 'ILS',
    vatRate: 18.0,
    documentTemplate: 'default',
    paginationLimit: 50,
  },

  // Israeli tax settings
  israeliTax: {
    defaultVatRate: 18.0,
    vatReportingPeriods: {
      MONTHLY: 'monthly',
      BI_MONTHLY: 'bi_monthly',
    },
    documentNumberPadding: 5,
  },

  // File upload settings
  fileUpload: {
    maxSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
    storageBucket: 'documents',
  },

  // Email settings
  email: {
    templates: {
      documentSend: {
        subject: (docType: string, docNumber: string) => 
          `${getDocumentTypeHebrew(docType)} מספר ${docNumber}`,
        body: (docType: string, docNumber: string, date: string, amount: string, currency: string, pdfUrl: string, companyName: string) => `
שלום,

מצורף ${getDocumentTypeHebrew(docType)} מספר ${docNumber}
מתאריך ${date}
על סך ${amount} ${currency}

לצפייה והורדה: ${pdfUrl}

תודה,
${companyName}
        `.trim(),
      },
    },
  },

  // WhatsApp settings
  whatsapp: {
    messageTemplate: (docType: string, docNumber: string, date: string, amount: string, currency: string, pdfUrl: string, companyName: string) => `
שלום,

מצורף ${getDocumentTypeHebrew(docType)} מספר ${docNumber}
מתאריך ${date}
על סך ${amount} ${currency}

לצפייה והורדה:
${pdfUrl}

תודה,
${companyName}
    `.trim(),
  },

  // ITA (Israeli Tax Authority) settings
  ita: {
    apiUrl: 'https://api.taxes.gov.il/shaam/v1',
    retryAttempts: 5,
    retryDelays: [5 * 60 * 1000, 15 * 60 * 1000, 60 * 60 * 1000, 4 * 60 * 60 * 1000, 12 * 60 * 60 * 1000], // in milliseconds
    documentTypes: {
      'tax_invoice': 'חשבונית מס',
      'receipt': 'קבלה',
      'credit_note': 'זיכוי',
      'tax_invoice_receipt': 'חשבונית מס/קבלה',
    },
  },

  // Audit log settings
  audit: {
    retentionDays: 2555, // 7 years as required by Israeli law
    actions: {
      CREATE: 'create',
      UPDATE: 'update',
      DELETE: 'delete',
      VIEW: 'view',
      SEND: 'send',
      APPROVE: 'approve',
      REJECT: 'reject',
      LOGIN: 'login',
      LOGOUT: 'logout',
      SEARCH: 'search',
      GENERATE: 'generate',
    },
    entityTypes: {
      USER: 'user',
      COMPANY: 'company',
      CUSTOMER: 'customer',
      DOCUMENT: 'document',
      EXPENSE: 'expense',
      PRODUCT: 'product',
      REPORT: 'report',
      USER_REGISTRATION: 'user_registration',
      USER_SESSION: 'user_session',
      DOCUMENT_SEQUENCE: 'document_sequence',
      VAT_REPORT: 'vat_report',
    },
  },

  // Error messages
  errors: {
    UNAUTHORIZED: 'Unauthorized access',
    FORBIDDEN: 'Insufficient permissions',
    NOT_FOUND: 'Resource not found',
    VALIDATION_FAILED: 'Validation failed',
    RATE_LIMITED: 'Too many requests',
    INTERNAL_ERROR: 'Internal server error',
    DUPLICATE_RESOURCE: 'Resource already exists',
    INVALID_TOKEN: 'Invalid or expired token',
    COMPANY_ACCESS_DENIED: 'No access to company',
    DOCUMENT_NOT_SENDABLE: 'Document is not in a sendable state',
    EXPENSE_NOT_PENDING: 'Only pending expenses can be modified',
  },

  // Success messages
  success: {
    USER_REGISTERED: 'User registered successfully',
    USER_LOGGED_IN: 'Login successful',
    DOCUMENT_CREATED: 'Document created successfully',
    DOCUMENT_SENT: 'Document sent successfully',
    CUSTOMER_CREATED: 'Customer created successfully',
    EXPENSE_APPROVED: 'Expense approved successfully',
    EXPENSE_REJECTED: 'Expense rejected successfully',
    REPORT_GENERATED: 'Report generated successfully',
  },
};

// Helper function to get document type in Hebrew
function getDocumentTypeHebrew(type: string): string {
  const types: Record<string, string> = {
    'tax_invoice': 'חשבונית מס',
    'receipt': 'קבלה',
    'credit_note': 'זיכוי',
    'tax_invoice_receipt': 'חשבונית מס/קבלה',
  };
  return types[type] || type;
}

// Helper function to format Israeli date
export function formatIsraeliDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('he-IL');
}

// Helper function to format Israeli currency
export function formatIsraeliCurrency(amount: number, currency: string = 'ILS'): string {
  return new Intl.NumberFormat('he-IL', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}

// Helper function to validate Israeli business entity
export function validateIsraeliBusinessEntity(businessNumber: string, vatId?: string): boolean {
  // Basic validation - in real implementation you might want to call external APIs
  if (!config.validation.businessNumber.test(businessNumber)) {
    return false;
  }
  
  if (vatId && !config.validation.vatId.test(vatId)) {
    return false;
  }
  
  return true;
}

export default config;
