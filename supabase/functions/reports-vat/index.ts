import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  logAudit,
} from '../_shared/utils.ts';
import { VATReportResponse } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'GET') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Get query parameters
    const url = new URL(req.url);
    const companyId = url.searchParams.get('company_id');
    const periodStart = url.searchParams.get('period_start');
    const periodEnd = url.searchParams.get('period_end');

    if (!companyId) {
      return errorResponse('company_id is required', 400);
    }

    if (!periodStart || !periodEnd) {
      return errorResponse('period_start and period_end are required', 400);
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(periodStart) || !dateRegex.test(periodEnd)) {
      return errorResponse('Dates must be in YYYY-MM-DD format', 400);
    }

    // Check user access to company
    await checkUserRole(user.id, companyId, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    // Get sales documents (invoices) for the period
    const { data: salesDocuments, error: salesError } = await supabase
      .from('documents')
      .select(`
        *,
        customer:customers(name_hebrew, business_number),
        items:document_items(*)
      `)
      .eq('company_id', companyId)
      .in('document_type', ['tax_invoice', 'tax_invoice_receipt'])
      .in('status', ['approved', 'sent', 'paid'])
      .gte('issue_date', periodStart)
      .lte('issue_date', periodEnd)
      .order('issue_date', { ascending: true });

    if (salesError) {
      console.error('Sales documents error:', salesError);
      return errorResponse('Failed to fetch sales documents', 500);
    }

    // Get purchase expenses for the period
    const { data: purchaseExpenses, error: expensesError } = await supabase
      .from('expenses')
      .select('*')
      .eq('company_id', companyId)
      .eq('status', 'approved')
      .gte('expense_date', periodStart)
      .lte('expense_date', periodEnd)
      .order('expense_date', { ascending: true });

    if (expensesError) {
      console.error('Purchase expenses error:', expensesError);
      return errorResponse('Failed to fetch purchase expenses', 500);
    }

    // Calculate sales totals
    const salesTotals = calculateSalesTotals(salesDocuments || []);
    
    // Calculate purchases totals
    const purchasesTotals = calculatePurchasesTotals(purchaseExpenses || []);

    // Calculate VAT liability
    const vatLiability = salesTotals.vat_collected - purchasesTotals.vat_paid;

    // Generate export URLs (placeholder implementation)
    const exportFormats = await generateExportUrls(companyId, periodStart, periodEnd, {
      sales: salesTotals,
      purchases: purchasesTotals,
      vatLiability
    });

    // Log audit
    await logAudit(
      companyId,
      user.id,
      'generate',
      'vat_report',
      `${periodStart}_${periodEnd}`,
      null,
      { 
        period_start: periodStart, 
        period_end: periodEnd,
        vat_liability: vatLiability 
      },
      req
    );

    const response: VATReportResponse = {
      period: `${periodStart} to ${periodEnd}`,
      sales: {
        total_before_vat: salesTotals.total_before_vat,
        vat_collected: salesTotals.vat_collected,
        total_with_vat: salesTotals.total_with_vat,
        documents: salesTotals.documents,
      },
      purchases: {
        total_before_vat: purchasesTotals.total_before_vat,
        vat_paid: purchasesTotals.vat_paid,
        total_with_vat: purchasesTotals.total_with_vat,
        expenses: purchasesTotals.expenses,
      },
      vat_liability: vatLiability,
      export_formats: exportFormats,
    };

    return successResponse(response);

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});

function calculateSalesTotals(documents: any[]) {
  let totalBeforeVat = 0;
  let vatCollected = 0;
  let totalWithVat = 0;

  const documentSummaries = documents.map(doc => {
    totalBeforeVat += doc.subtotal;
    vatCollected += doc.vat_amount;
    totalWithVat += doc.total_amount;

    return {
      id: doc.id,
      document_number: doc.document_number,
      document_type: doc.document_type,
      customer_name: doc.customer?.name_hebrew,
      customer_business_number: doc.customer?.business_number,
      issue_date: doc.issue_date,
      subtotal: doc.subtotal,
      vat_amount: doc.vat_amount,
      total_amount: doc.total_amount,
      currency: doc.currency,
    };
  });

  return {
    total_before_vat: Math.round(totalBeforeVat * 100) / 100,
    vat_collected: Math.round(vatCollected * 100) / 100,
    total_with_vat: Math.round(totalWithVat * 100) / 100,
    documents: documentSummaries,
  };
}

function calculatePurchasesTotals(expenses: any[]) {
  let totalBeforeVat = 0;
  let vatPaid = 0;
  let totalWithVat = 0;

  const expenseSummaries = expenses.map(expense => {
    totalBeforeVat += expense.amount;
    vatPaid += expense.vat_amount;
    totalWithVat += expense.total_amount;

    return {
      id: expense.id,
      expense_number: expense.expense_number,
      vendor_name: expense.vendor_name,
      expense_date: expense.expense_date,
      category: expense.category,
      amount: expense.amount,
      vat_amount: expense.vat_amount,
      total_amount: expense.total_amount,
      currency: expense.currency,
    };
  });

  return {
    total_before_vat: Math.round(totalBeforeVat * 100) / 100,
    vat_paid: Math.round(vatPaid * 100) / 100,
    total_with_vat: Math.round(totalWithVat * 100) / 100,
    expenses: expenseSummaries,
  };
}

async function generateExportUrls(companyId: string, periodStart: string, periodEnd: string, data: any) {
  // In a real implementation, you would generate actual PDF, Excel, and PCN874 files
  // and upload them to storage, then return the URLs
  
  const baseUrl = Deno.env.get('SUPABASE_URL');
  const timestamp = new Date().getTime();
  
  return {
    pdf_url: `${baseUrl}/storage/v1/object/public/reports/${companyId}/vat-report-${periodStart}-${periodEnd}-${timestamp}.pdf`,
    excel_url: `${baseUrl}/storage/v1/object/public/reports/${companyId}/vat-report-${periodStart}-${periodEnd}-${timestamp}.xlsx`,
    pcn874_url: `${baseUrl}/storage/v1/object/public/reports/${companyId}/pcn874-${periodStart}-${periodEnd}-${timestamp}.xml`,
  };
}
