import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders, errorResponse, successResponse } from '../_shared/utils.ts';
import { authenticateUser, logAudit } from '../_shared/utils.ts';

interface EmailScanResult {
  scanned_count: number;
  new_expenses: number;
  duplicates: number;
  errors: string[];
}

interface ExtractedInvoiceData {
  vendor_name?: string;
  date?: string;
  invoice_number?: string;
  amount_before_vat?: number;
  vat_amount?: number;
  total_amount?: number;
  currency?: string;
  is_invoice: boolean;
}

interface EmailAttachment {
  filename: string;
  content: Uint8Array;
  contentType: string;
  size: number;
}

function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
}

async function connectToEmailProvider(emailAccount: any): Promise<any> {
  // Decrypt access token (placeholder implementation)
  const accessToken = emailAccount.access_token; // In real implementation, decrypt this
  
  if (emailAccount.provider === 'gmail') {
    // Gmail API connection
    return {
      provider: 'gmail',
      accessToken,
      apiUrl: 'https://gmail.googleapis.com/gmail/v1'
    };
  } else if (emailAccount.provider === 'outlook') {
    // Microsoft Graph API connection
    return {
      provider: 'outlook',
      accessToken,
      apiUrl: 'https://graph.microsoft.com/v1.0'
    };
  }
  
  throw new Error(`Unsupported email provider: ${emailAccount.provider}`);
}

async function fetchEmailsWithAttachments(connection: any, sinceDate?: string): Promise<any[]> {
  const emails: any[] = [];
  
  try {
    if (connection.provider === 'gmail') {
      // Gmail API implementation
      const query = `has:attachment ${sinceDate ? `after:${sinceDate}` : ''}`;
      const response = await fetch(
        `${connection.apiUrl}/users/me/messages?q=${encodeURIComponent(query)}&maxResults=100`,
        {
          headers: {
            'Authorization': `Bearer ${connection.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        return data.messages || [];
      }
    } else if (connection.provider === 'outlook') {
      // Microsoft Graph API implementation
      const filter = `hasAttachments eq true${sinceDate ? ` and receivedDateTime ge ${sinceDate}` : ''}`;
      const response = await fetch(
        `${connection.apiUrl}/me/messages?$filter=${encodeURIComponent(filter)}&$top=100`,
        {
          headers: {
            'Authorization': `Bearer ${connection.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        return data.value || [];
      }
    }
  } catch (error) {
    console.error('Error fetching emails:', error);
  }
  
  return emails;
}

async function downloadAttachments(connection: any, emailId: string): Promise<EmailAttachment[]> {
  const attachments: EmailAttachment[] = [];
  
  try {
    if (connection.provider === 'gmail') {
      // Get email details with attachments
      const response = await fetch(
        `${connection.apiUrl}/users/me/messages/${emailId}`,
        {
          headers: {
            'Authorization': `Bearer ${connection.accessToken}`
          }
        }
      );
      
      if (response.ok) {
        const email = await response.json();
        // Process Gmail attachments (simplified)
        // In real implementation, you would parse the email structure and download attachments
      }
    } else if (connection.provider === 'outlook') {
      // Get Outlook attachments
      const response = await fetch(
        `${connection.apiUrl}/me/messages/${emailId}/attachments`,
        {
          headers: {
            'Authorization': `Bearer ${connection.accessToken}`
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        // Process Outlook attachments (simplified)
        // In real implementation, you would download the actual attachment content
      }
    }
  } catch (error) {
    console.error('Error downloading attachments:', error);
  }
  
  return attachments;
}

function isRelevantFile(attachment: EmailAttachment): boolean {
  const relevantTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  return relevantTypes.includes(attachment.contentType) && attachment.size <= maxSize;
}

async function extractTextWithOCR(fileContent: Uint8Array, contentType: string): Promise<string> {
  // TODO: Implement actual OCR using Tesseract or similar
  // For now, return placeholder text
  if (contentType === 'application/pdf') {
    return "PDF content extracted via OCR (placeholder)";
  } else if (contentType.startsWith('image/')) {
    return "Image content extracted via OCR (placeholder)";
  }
  return "";
}

async function extractInvoiceDataWithAI(text: string): Promise<ExtractedInvoiceData> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
  
  if (!openaiApiKey) {
    return { is_invoice: false };
  }
  
  const prompt = `Extract invoice/receipt data from this text.
Return JSON with:
- vendor_name (Hebrew or English)
- date (YYYY-MM-DD)
- invoice_number
- amount_before_vat
- vat_amount
- total_amount
- currency
- is_invoice (boolean)
If not an invoice/receipt, return {is_invoice: false}

Text: ${text}`;

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 500
      })
    });

    if (response.ok) {
      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (content) {
        try {
          return JSON.parse(content);
        } catch (parseError) {
          console.error('Failed to parse AI response:', parseError);
        }
      }
    }
  } catch (error) {
    console.error('OpenAI API error:', error);
  }
  
  return { is_invoice: false };
}

async function detectDuplicateExpense(
  supabase: any,
  companyId: string,
  vendorName: string,
  amount: number,
  expenseDate: string
): Promise<{ risk_level: 'none' | 'low' | 'high'; potential_duplicate_id?: string }> {
  // Normalize vendor name
  const normalizedVendor = vendorName
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+(ltd|inc|בע"מ|חברה|קבוצת).*$/i, '');

  // Query existing expenses within date range
  const dateRange = new Date(expenseDate);
  const startDate = new Date(dateRange.getTime() - 7 * 24 * 60 * 60 * 1000);
  const endDate = new Date(dateRange.getTime() + 7 * 24 * 60 * 60 * 1000);

  const { data: existingExpenses } = await supabase
    .from('expenses')
    .select('id, vendor_name, total_amount, expense_date')
    .eq('company_id', companyId)
    .neq('status', 'rejected')
    .gte('expense_date', startDate.toISOString().split('T')[0])
    .lte('expense_date', endDate.toISOString().split('T')[0]);

  if (!existingExpenses || existingExpenses.length === 0) {
    return { risk_level: 'none' };
  }

  let highestScore = 0;
  let potentialDuplicateId: string | undefined;

  for (const expense of existingExpenses) {
    // Calculate similarity scores
    const normalizedExistingVendor = expense.vendor_name
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+(ltd|inc|בע"מ|חברה|קבוצת).*$/i, '');

    // Name similarity
    let nameScore = 0;
    if (normalizedVendor === normalizedExistingVendor) {
      nameScore = 100;
    } else if (normalizedVendor.includes(normalizedExistingVendor) || normalizedExistingVendor.includes(normalizedVendor)) {
      nameScore = 60;
    }

    // Amount similarity
    let amountScore = 0;
    const amountDiff = Math.abs(amount - expense.total_amount) / amount;
    if (amountDiff === 0) {
      amountScore = 100;
    } else if (amountDiff <= 0.01) {
      amountScore = 90;
    } else if (amountDiff <= 0.05) {
      amountScore = 70;
    }

    // Date similarity
    let dateScore = 0;
    const daysDiff = Math.abs(new Date(expenseDate).getTime() - new Date(expense.expense_date).getTime()) / (24 * 60 * 60 * 1000);
    if (daysDiff === 0) {
      dateScore = 100;
    } else if (daysDiff <= 1) {
      dateScore = 80;
    } else if (daysDiff <= 3) {
      dateScore = 60;
    }

    // Total score
    const totalScore = (nameScore * 0.4) + (amountScore * 0.4) + (dateScore * 0.2);

    if (totalScore > highestScore) {
      highestScore = totalScore;
      potentialDuplicateId = expense.id;
    }
  }

  if (highestScore >= 95) {
    return { risk_level: 'high', potential_duplicate_id: potentialDuplicateId };
  } else if (highestScore >= 80) {
    return { risk_level: 'low', potential_duplicate_id: potentialDuplicateId };
  }

  return { risk_level: 'none' };
}

function autoCategorizeExpense(vendorName: string): string {
  const categories: Record<string, string[]> = {
    'office_supplies': ['נייר', 'עטים', 'משרד', 'office', 'supplies'],
    'utilities': ['חשמל', 'מים', 'גז', 'electric', 'water', 'gas'],
    'marketing': ['פרסום', 'שיווק', 'marketing', 'advertising'],
    'travel': ['נסיעות', 'דלק', 'travel', 'fuel', 'gas station'],
    'meals': ['מסעדה', 'אוכל', 'restaurant', 'food'],
    'professional_services': ['עורך דין', 'רואה חשבון', 'lawyer', 'accountant']
  };

  const normalizedVendor = vendorName.toLowerCase();
  
  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => normalizedVendor.includes(keyword))) {
      return category;
    }
  }
  
  return 'other';
}

async function scanEmailForExpenses(emailAccountId: string, sinceDate?: string): Promise<EmailScanResult> {
  const supabase = createSupabaseClient();
  const result: EmailScanResult = {
    scanned_count: 0,
    new_expenses: 0,
    duplicates: 0,
    errors: []
  };

  try {
    // 1. Fetch email account credentials
    const { data: emailAccount, error: accountError } = await supabase
      .from('email_accounts')
      .select('*')
      .eq('id', emailAccountId)
      .single();

    if (accountError || !emailAccount) {
      result.errors.push('Email account not found');
      return result;
    }

    // 2. Connect to email provider
    const connection = await connectToEmailProvider(emailAccount);

    // 3. Query emails with attachments
    const emails = await fetchEmailsWithAttachments(connection, sinceDate || emailAccount.last_sync_at);

    for (const email of emails) {
      try {
        result.scanned_count++;

        // 4. Download and process attachments
        const attachments = await downloadAttachments(connection, email.id);
        
        for (const attachment of attachments) {
          if (!isRelevantFile(attachment)) {
            continue;
          }

          // 5. Upload to Supabase Storage
          const fileName = `${emailAccountId}/${Date.now()}_${attachment.filename}`;
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('expenses')
            .upload(fileName, attachment.content, {
              contentType: attachment.contentType
            });

          if (uploadError) {
            result.errors.push(`Failed to upload ${attachment.filename}: ${uploadError.message}`);
            continue;
          }

          // 6. Extract text with OCR
          const extractedText = await extractTextWithOCR(attachment.content, attachment.contentType);

          // 7. Process with AI
          const aiData = await extractInvoiceDataWithAI(extractedText);

          if (aiData.is_invoice && aiData.vendor_name && aiData.total_amount) {
            // 8. Check for duplicates
            const duplicateCheck = await detectDuplicateExpense(
              supabase,
              emailAccount.company_id,
              aiData.vendor_name,
              aiData.total_amount,
              aiData.date || new Date().toISOString().split('T')[0]
            );

            if (duplicateCheck.risk_level === 'high') {
              result.duplicates++;
              continue;
            }

            // 9. Create expense record
            const { error: expenseError } = await supabase
              .from('expenses')
              .insert({
                company_id: emailAccount.company_id,
                vendor_name: aiData.vendor_name,
                expense_date: aiData.date || new Date().toISOString().split('T')[0],
                amount: aiData.amount_before_vat || 0,
                vat_amount: aiData.vat_amount || 0,
                total_amount: aiData.total_amount,
                currency: aiData.currency || 'ILS',
                category: autoCategorizeExpense(aiData.vendor_name),
                status: 'pending',
                duplicate_risk: duplicateCheck.risk_level,
                source: 'email_scan',
                source_email_id: email.id,
                original_file_url: uploadData?.path,
                extracted_data: aiData
              });

            if (expenseError) {
              result.errors.push(`Failed to create expense: ${expenseError.message}`);
            } else {
              result.new_expenses++;
            }
          }
        }
      } catch (emailError) {
        result.errors.push(`Error processing email ${email.id}: ${emailError.message}`);
      }
    }

    // 10. Update last sync time
    await supabase
      .from('email_accounts')
      .update({ last_sync_at: new Date().toISOString() })
      .eq('id', emailAccountId);

    return result;
  } catch (error) {
    result.errors.push(`Scan error: ${error.message}`);
    return result;
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // This function can be called via cron or manually
    const { email_account_id, since_date } = await req.json();

    if (!email_account_id) {
      return errorResponse('Email account ID is required', 400);
    }

    const result = await scanEmailForExpenses(email_account_id, since_date);

    return successResponse(result, 'Email scan completed');
  } catch (error) {
    console.error('Email scan error:', error);
    return errorResponse('Internal server error', 500);
  }
});
