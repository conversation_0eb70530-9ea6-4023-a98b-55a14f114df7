// Business Logic Functions Integration Tests
// Run with: deno test --allow-net --allow-env business-logic.test.ts

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.168.0/testing/asserts.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Test configuration - use real Supabase instance
const supabaseUrl = Deno.env.get('VITE_SUPABASE_URL') || 'https://zhwqtgypoueykwgphmqn.supabase.co';
const supabaseKey = Deno.env.get('VITE_SUPABASE_PUBLISHABLE_KEY') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpod3F0Z3lwb3VleWt3Z3BobXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY0NjgyOTQsImV4cCI6MjA3MjA0NDI5NH0.OO6IgJ4oZQvP0T0lJd5OUgN2_apHT9xqpGhZbHXy0lM';

const supabase = createClient(supabaseUrl, supabaseKey);

// Test data
const testCompanyId = 'test-company-bl-123';
const testUserId = 'test-user-bl-123';
const testDocumentId = 'test-document-bl-123';
const testCustomerId = 'test-customer-bl-123';

// Helper function to create test data
async function createTestData() {
  console.log('Creating test data...');
  
  // Create test company
  await supabase.from('companies').upsert({
    id: testCompanyId,
    name_hebrew: 'חברת בדיקה לוגיקה עסקית',
    name_english: 'Business Logic Test Company',
    business_number: '*********',
    vat_id: '*********',
    email: '<EMAIL>',
    phone: '03-1234567',
    address_hebrew: 'רחוב הבדיקה 1, תל אביב',
    created_at: new Date().toISOString()
  });

  // Create test customer
  await supabase.from('customers').upsert({
    id: testCustomerId,
    company_id: testCompanyId,
    name_hebrew: 'לקוח בדיקה לוגיקה',
    name_english: 'Business Logic Test Customer',
    business_number: '*********',
    vat_id: '*********',
    email: '<EMAIL>',
    phone: '03-7654321',
    billing_address_hebrew: 'רחוב הלקוח 2, תל אביב',
    created_at: new Date().toISOString()
  });

  // Create test document
  await supabase.from('documents').upsert({
    id: testDocumentId,
    company_id: testCompanyId,
    customer_id: testCustomerId,
    document_type: 'tax_invoice',
    document_number: 'BL-INV-2025-0001',
    issue_date: '2025-01-01',
    currency: 'ILS',
    subtotal: 100.00,
    vat_amount: 17.00,
    total_amount: 117.00,
    status: 'pending_allocation',
    template_id: 'modern',
    created_by: testUserId,
    created_at: new Date().toISOString()
  });

  // Create test document items
  await supabase.from('document_items').upsert({
    id: 'test-item-bl-123',
    document_id: testDocumentId,
    description_hebrew: 'פריט בדיקה לוגיקה עסקית',
    description_english: 'Business Logic Test Item',
    quantity: 1,
    unit_price: 100.00,
    vat_rate: 17,
    line_total: 100.00,
    vat_amount: 17.00,
    line_number: 1
  });

  console.log('Test data created successfully');
}

// Helper function to cleanup test data
async function cleanupTestData() {
  console.log('Cleaning up test data...');
  
  await supabase.from('document_items').delete().eq('document_id', testDocumentId);
  await supabase.from('documents').delete().eq('id', testDocumentId);
  await supabase.from('customers').delete().eq('id', testCustomerId);
  await supabase.from('companies').delete().eq('id', testCompanyId);
  await supabase.from('ita_queue').delete().eq('document_id', testDocumentId);
  await supabase.from('expenses').delete().eq('company_id', testCompanyId);
  await supabase.from('email_accounts').delete().eq('company_id', testCompanyId);
  
  console.log('Test data cleaned up successfully');
}

// Helper function to make function requests
async function callFunction(functionName: string, payload: any = {}) {
  const response = await fetch(`${supabaseUrl}/functions/v1/${functionName}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${supabaseKey}`,
    },
    body: JSON.stringify(payload)
  });
  
  const data = await response.json();
  return { response, data };
}

Deno.test("Business Logic Functions - ITA SHAAM Integration", async (t) => {
  await createTestData();
  
  await t.step("should handle ITA document submission", async () => {
    const { response, data } = await callFunction('ita-submit-document', {
      document_id: testDocumentId
    });
    
    // Should handle the submission (may fail due to test environment but should not crash)
    assert(response.status === 200 || response.status === 400);
    assertExists(data);
    
    if (response.status === 200) {
      console.log('ITA submission successful:', data);
    } else {
      console.log('ITA submission failed as expected in test environment:', data);
    }
  });
  
  await cleanupTestData();
});

Deno.test("Business Logic Functions - Retry Queue Processing", async (t) => {
  await createTestData();
  
  await t.step("should process ITA retry queue", async () => {
    // First add an item to the queue
    const { error: queueError } = await supabase.from('ita_queue').upsert({
      id: 'test-queue-bl-123',
      document_id: testDocumentId,
      status: 'pending',
      attempts: 0,
      request_payload: { document_id: testDocumentId },
      next_retry_at: new Date().toISOString(),
      created_at: new Date().toISOString()
    });
    
    assert(!queueError, `Failed to create queue item: ${queueError?.message}`);

    const { response, data } = await callFunction('process-ita-queue');
    
    assertEquals(response.status, 200);
    assertExists(data.data);
    assertExists(data.data.processed);
    
    console.log('Queue processing result:', data.data);
    
    // Cleanup queue item
    await supabase.from('ita_queue').delete().eq('id', 'test-queue-bl-123');
  });
  
  await cleanupTestData();
});

Deno.test("Business Logic Functions - Document PDF Generation", async (t) => {
  await createTestData();
  
  await t.step("should generate document PDF", async () => {
    const { response, data } = await callFunction('generate-pdf', {
      document_id: testDocumentId,
      template_id: 'modern'
    });
    
    assertEquals(response.status, 200);
    assertExists(data.data);
    assertExists(data.data.pdf_url);
    assertExists(data.data.pdf_size);
    
    console.log('PDF generation result:', data.data);
    
    // Verify the document was updated with PDF URL
    const { data: document } = await supabase
      .from('documents')
      .select('pdf_url')
      .eq('id', testDocumentId)
      .single();
    
    assertExists(document?.pdf_url);
  });
  
  await cleanupTestData();
});

Deno.test("Business Logic Functions - Email Scanning", async (t) => {
  await createTestData();
  
  await t.step("should handle email scanning for expenses", async () => {
    // Create test email account
    const emailAccountId = 'test-email-bl-123';
    const { error: emailError } = await supabase.from('email_accounts').upsert({
      id: emailAccountId,
      company_id: testCompanyId,
      provider: 'gmail',
      email_address: '<EMAIL>',
      access_token: 'test-token-encrypted',
      last_sync_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date().toISOString()
    });
    
    assert(!emailError, `Failed to create email account: ${emailError?.message}`);

    const { response, data } = await callFunction('scan-emails', {
      email_account_id: emailAccountId
    });
    
    assertEquals(response.status, 200);
    assertExists(data.data);
    assertExists(data.data.scanned_count);
    assertExists(data.data.new_expenses);
    assertExists(data.data.duplicates);
    
    console.log('Email scanning result:', data.data);
    
    // Cleanup email account
    await supabase.from('email_accounts').delete().eq('id', emailAccountId);
  });
  
  await cleanupTestData();
});

Deno.test("Utility Functions - Duplicate Detection", async (t) => {
  await createTestData();
  
  await t.step("should detect duplicate expenses correctly", async () => {
    // Create test expenses for duplicate detection
    const expense1Id = 'test-expense-bl-1';
    const expense2Id = 'test-expense-bl-2';
    
    await supabase.from('expenses').upsert([
      {
        id: expense1Id,
        company_id: testCompanyId,
        vendor_name: 'ספק בדיקה לוגיקה',
        expense_date: '2025-01-01',
        total_amount: 100.00,
        status: 'approved',
        created_at: new Date().toISOString()
      },
      {
        id: expense2Id,
        company_id: testCompanyId,
        vendor_name: 'ספק אחר לוגיקה',
        expense_date: '2025-01-02',
        total_amount: 200.00,
        status: 'approved',
        created_at: new Date().toISOString()
      }
    ]);

    // Import and test duplicate detection utility
    const { detectDuplicateExpense } = await import('../_shared/utils.ts');
    
    // Test exact match (should be high risk)
    const result1 = await detectDuplicateExpense(
      supabase,
      testCompanyId,
      'ספק בדיקה לוגיקה',
      100.00,
      '2025-01-01'
    );
    assertEquals(result1.risk_level, 'high');
    assertExists(result1.potential_duplicate_id);
    console.log('High risk duplicate detected:', result1);
    
    // Test similar match (should be low risk)
    const result2 = await detectDuplicateExpense(
      supabase,
      testCompanyId,
      'ספק בדיקה',
      101.00,
      '2025-01-02'
    );
    assert(result2.risk_level === 'low' || result2.risk_level === 'none');
    console.log('Similar vendor result:', result2);
    
    // Test no match (should be none)
    const result3 = await detectDuplicateExpense(
      supabase,
      testCompanyId,
      'ספק חדש לגמרי',
      500.00,
      '2025-01-15'
    );
    assertEquals(result3.risk_level, 'none');
    console.log('No duplicate result:', result3);
    
    // Cleanup
    await supabase.from('expenses').delete().in('id', [expense1Id, expense2Id]);
  });
  
  await cleanupTestData();
});

Deno.test("Utility Functions - WhatsApp Message Builder", async (t) => {
  await createTestData();
  
  await t.step("should build WhatsApp message correctly", async () => {
    const { buildWhatsAppMessage } = await import('../_shared/utils.ts');
    
    const result = await buildWhatsAppMessage(
      supabase,
      testDocumentId,
      '+972501234567'
    );
    
    assertExists(result.whatsapp_url);
    assertExists(result.message_text);
    assert(result.whatsapp_url.includes('wa.me'));
    assert(result.whatsapp_url.includes('972501234567'));
    assert(result.message_text.includes('חשבונית מס'));
    assert(result.message_text.includes('BL-INV-2025-0001'));
    assert(result.message_text.includes('חברת בדיקה לוגיקה עסקית'));
    
    console.log('WhatsApp URL:', result.whatsapp_url);
    console.log('Message text:', result.message_text);
  });
  
  await cleanupTestData();
});

console.log('All business logic function tests completed!');
