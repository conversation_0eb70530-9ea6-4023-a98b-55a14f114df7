# Fintech Application API Endpoints

This directory contains all the Supabase Edge Functions that implement the API endpoints for the Israeli B2B invoicing application as specified in the master architecture document.

## Overview

The API is built using Supabase Edge Functions (Deno runtime) and implements a complete fintech solution with the following features:

- **Multi-tenant architecture** with company-based data isolation
- **Row Level Security (RLS)** for data protection
- **Israeli tax compliance** including ITA SHAAM integration
- **Comprehensive audit logging**
- **Rate limiting** and security measures
- **Hebrew/English bilingual support**

## API Endpoints

### Authentication
- `POST /auth-register` - User and company registration
- `POST /auth-login` - User authentication

### Document Management
- `GET /documents-next-number` - Get next sequential document number
- `POST /documents-create` - Create new document (invoice, receipt, etc.)
- `POST /documents-send` - Send document via email or WhatsApp

### Customer Management
- `POST /customers-create` - Create new customer
- `GET /customers-search` - Search customers by name or business number

### Expense Management
- `PATCH /expenses-update-status` - Approve/reject expenses (accountant only)

### Reporting
- `GET /reports-vat` - Generate VAT reports with export options

## Directory Structure

```
supabase/functions/
├── _shared/                    # Shared utilities and types
│   ├── types.ts               # TypeScript type definitions
│   ├── utils.ts               # Common utility functions
│   ├── config.ts              # Configuration and constants
│   └── database-functions.sql # Database functions
├── _tests/                    # Integration tests
│   └── integration.test.ts    # Comprehensive API tests
├── auth-register/             # User registration endpoint
├── auth-login/                # User login endpoint
├── documents-next-number/     # Document numbering endpoint
├── documents-create/          # Document creation endpoint
├── documents-send/            # Document sending endpoint
├── customers-create/          # Customer creation endpoint
├── customers-search/          # Customer search endpoint
├── expenses-update-status/    # Expense approval endpoint
├── reports-vat/              # VAT reporting endpoint
└── README.md                 # This file
```

## Setup Instructions

### 1. Database Setup

First, execute the database functions in your Supabase SQL editor:

```sql
-- Copy and execute the contents of _shared/database-functions.sql
```

### 2. Environment Variables

Ensure the following environment variables are set in your Supabase project:

```bash
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_ANON_KEY=your_anon_key
```

### 3. Deploy Functions

Deploy all functions to Supabase:

```bash
# Deploy all functions
supabase functions deploy

# Or deploy individual functions
supabase functions deploy auth-register
supabase functions deploy auth-login
supabase functions deploy documents-create
# ... etc
```

### 4. Test the API

Run the integration tests:

```bash
cd supabase/functions/_tests
deno test --allow-net --allow-env integration.test.ts
```

## API Usage Examples

### Register a New User and Company

```typescript
const response = await fetch('/functions/v1/auth-register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'SecurePass123',
    full_name: 'John Doe',
    phone: '**********',
    company: {
      business_number: '*********',
      name: 'חברת דוגמה',
      vat_id: '*********',
      address_hebrew: 'רחוב הדוגמה 1',
      city_hebrew: 'תל אביב',
      phone: '**********',
      industry: 'Technology',
      interested_in_accounting: true
    }
  })
});
```

### Create a Customer

```typescript
const response = await fetch('/functions/v1/customers-create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  },
  body: JSON.stringify({
    company_id: 'company-uuid',
    business_number: '*********',
    name_hebrew: 'לקוח דוגמה',
    vat_id: '*********',
    billing_address_hebrew: 'רחוב הלקוח 2',
    city_hebrew: 'חיפה',
    contact_email: '<EMAIL>'
  })
});
```

### Create an Invoice

```typescript
const response = await fetch('/functions/v1/documents-create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  },
  body: JSON.stringify({
    company_id: 'company-uuid',
    document_type: 'tax_invoice',
    customer_id: 'customer-uuid',
    issue_date: '2025-01-15',
    due_date: '2025-02-15',
    currency: 'ILS',
    items: [
      {
        description_hebrew: 'שירות ייעוץ',
        quantity: 1,
        unit_price: 1000,
        vat_rate: 18
      }
    ]
  })
});
```

### Generate VAT Report

```typescript
const response = await fetch(
  `/functions/v1/reports-vat?company_id=${companyId}&period_start=2025-01-01&period_end=2025-01-31`,
  {
    headers: { 'Authorization': `Bearer ${authToken}` }
  }
);
```

## Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (admin, user, accountant)
- Company-based data isolation
- Row Level Security (RLS) policies

### Rate Limiting
- Registration: 5 requests per 15 minutes
- Login: 10 requests per 15 minutes
- Document creation: 30 requests per minute
- Other endpoints: Various limits based on usage patterns

### Input Validation
- Israeli business number validation (9 digits)
- VAT ID validation (9 digits)
- Israeli phone number validation
- Email format validation
- Password strength requirements

### Audit Logging
- All actions are logged with user, timestamp, and details
- IP address and user agent tracking
- 7-year retention as required by Israeli law

## Error Handling

All endpoints return standardized error responses:

```typescript
{
  "success": false,
  "error": "Error message",
  "validation_errors": [
    {
      "field": "field_name",
      "message": "Validation error message"
    }
  ]
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request / Validation Error
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Israeli Tax Compliance

### ITA SHAAM Integration
- Automatic submission of approved documents
- Retry mechanism with exponential backoff
- Allocation number tracking
- Error handling and reporting

### Document Numbering
- Sequential numbering per document type
- Year-based prefixes
- Atomic increment to prevent duplicates

### VAT Calculations
- 18% default VAT rate
- Support for multiple VAT rates
- Proper rounding according to Israeli standards

## Monitoring and Maintenance

### Health Checks
Each function includes error handling and logging for monitoring.

### Performance
- Optimized database queries
- Efficient pagination
- Caching where appropriate

### Scalability
- Stateless functions
- Database connection pooling
- Rate limiting to prevent abuse

## Support

For issues or questions regarding the API implementation, refer to:
- Master Architecture Document
- Design System Documentation
- Supabase Documentation
- Israeli Tax Authority guidelines
