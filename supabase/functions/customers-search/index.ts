import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  logAudit,
} from '../_shared/utils.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'GET') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Get query parameters
    const url = new URL(req.url);
    const companyId = url.searchParams.get('company_id');
    const query = url.searchParams.get('query');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    if (!companyId) {
      return errorResponse('company_id is required', 400);
    }

    // Check user access to company
    await checkUserRole(user.id, companyId, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    let queryBuilder = supabase
      .from('customers')
      .select('*')
      .eq('company_id', companyId)
      .order('name_hebrew', { ascending: true })
      .range(offset, offset + limit - 1);

    // Apply search filter if query provided
    if (query && query.trim().length > 0) {
      const searchTerm = query.trim();
      
      // Search in multiple fields using OR condition
      queryBuilder = queryBuilder.or(`
        name_hebrew.ilike.%${searchTerm}%,
        name_english.ilike.%${searchTerm}%,
        business_number.ilike.%${searchTerm}%,
        contact_name.ilike.%${searchTerm}%,
        contact_email.ilike.%${searchTerm}%
      `);
    }

    const { data: customers, error: searchError } = await queryBuilder;

    if (searchError) {
      console.error('Customer search error:', searchError);
      return errorResponse('Failed to search customers', 500);
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('customers')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', companyId);

    if (query && query.trim().length > 0) {
      const searchTerm = query.trim();
      countQuery = countQuery.or(`
        name_hebrew.ilike.%${searchTerm}%,
        name_english.ilike.%${searchTerm}%,
        business_number.ilike.%${searchTerm}%,
        contact_name.ilike.%${searchTerm}%,
        contact_email.ilike.%${searchTerm}%
      `);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Customer count error:', countError);
    }

    // Log audit for search
    await logAudit(
      companyId,
      user.id,
      'search',
      'customer',
      'search_query',
      null,
      { query: query, results_count: customers?.length || 0 },
      req
    );

    return successResponse({
      customers: customers || [],
      pagination: {
        total: count || 0,
        limit: limit,
        offset: offset,
        has_more: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});
