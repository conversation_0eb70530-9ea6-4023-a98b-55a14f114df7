import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  logAudit,
} from '../_shared/utils.ts';
import { ValidationError } from '../_shared/types.ts';

interface UpdateExpenseStatusRequest {
  status: 'approved' | 'rejected';
  rejection_reason?: string;
}

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'PATCH') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Extract expense ID from URL
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const expenseId = pathParts[pathParts.indexOf('expenses') + 1];

    if (!expenseId) {
      return errorResponse('Expense ID is required', 400);
    }

    const body: UpdateExpenseStatusRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.status) {
      validationErrors.push({ field: 'status', message: 'Status is required' });
    } else if (!['approved', 'rejected'].includes(body.status)) {
      validationErrors.push({ field: 'status', message: 'Status must be approved or rejected' });
    }

    if (body.status === 'rejected' && (!body.rejection_reason || body.rejection_reason.trim().length < 3)) {
      validationErrors.push({ field: 'rejection_reason', message: 'Rejection reason is required when rejecting an expense' });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Get expense details
    const { data: expense, error: expenseError } = await supabase
      .from('expenses')
      .select('*')
      .eq('id', expenseId)
      .single();

    if (expenseError || !expense) {
      return errorResponse('Expense not found', 404);
    }

    // Check user access to company and ensure they have accountant role
    await checkUserRole(user.id, expense.company_id, ['accountant']);

    // Check if expense is in pending status
    if (expense.status !== 'pending') {
      return errorResponse('Only pending expenses can be approved or rejected', 400);
    }

    // Prepare update data
    const updateData: any = {
      status: body.status,
      approved_by: user.id,
      approved_at: new Date().toISOString(),
    };

    if (body.status === 'rejected') {
      updateData.rejection_reason = body.rejection_reason?.trim();
    }

    // Update expense
    const { data: updatedExpense, error: updateError } = await supabase
      .from('expenses')
      .update(updateData)
      .eq('id', expenseId)
      .select()
      .single();

    if (updateError) {
      console.error('Expense update error:', updateError);
      return errorResponse('Failed to update expense status', 500);
    }

    // Log audit
    await logAudit(
      expense.company_id,
      user.id,
      'update',
      'expense',
      expenseId,
      { status: expense.status },
      { 
        status: body.status,
        approved_by: user.id,
        rejection_reason: body.rejection_reason 
      },
      req
    );

    return successResponse(updatedExpense, `Expense ${body.status} successfully`);

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});
