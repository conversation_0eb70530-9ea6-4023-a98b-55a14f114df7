import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    const { device_token, platform, app_version, device_model, system_version } = await req.json()

    if (!device_token || !platform) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: device_token, platform' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Check if token already exists for this user
    const { data: existingToken, error: checkError } = await supabaseClient
      .from('push_tokens')
      .select('id')
      .eq('user_id', user.id)
      .eq('device_token', device_token)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing token:', checkError)
      return new Response(
        JSON.stringify({ error: 'Database error' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    if (existingToken) {
      // Update existing token
      const { error: updateError } = await supabaseClient
        .from('push_tokens')
        .update({
          app_version,
          device_model,
          system_version,
          last_used_at: new Date().toISOString(),
          is_active: true
        })
        .eq('id', existingToken.id)

      if (updateError) {
        console.error('Error updating push token:', updateError)
        return new Response(
          JSON.stringify({ error: 'Failed to update push token' }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Push token updated successfully',
          token_id: existingToken.id
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    } else {
      // Deactivate old tokens for this user on the same platform
      await supabaseClient
        .from('push_tokens')
        .update({ is_active: false })
        .eq('user_id', user.id)
        .eq('platform', platform)

      // Insert new token
      const { data: newToken, error: insertError } = await supabaseClient
        .from('push_tokens')
        .insert({
          user_id: user.id,
          device_token,
          platform,
          app_version,
          device_model,
          system_version,
          is_active: true,
          created_at: new Date().toISOString(),
          last_used_at: new Date().toISOString()
        })
        .select('id')
        .single()

      if (insertError) {
        console.error('Error inserting push token:', insertError)
        return new Response(
          JSON.stringify({ error: 'Failed to register push token' }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Push token registered successfully',
          token_id: newToken.id
        }),
        {
          status: 201,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

  } catch (error) {
    console.error('Error in push-token-register function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
