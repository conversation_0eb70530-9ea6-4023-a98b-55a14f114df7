import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders, errorResponse, successResponse } from '../_shared/utils.ts';
import { authenticateUser, logAudit } from '../_shared/utils.ts';

interface DocumentData {
  id: string;
  document_type: string;
  document_number: string;
  issue_date: string;
  due_date?: string;
  currency: string;
  subtotal: number;
  vat_amount: number;
  total_amount: number;
  notes?: string;
  template_id: string;
  company: any;
  customer: any;
  items: any[];
  ita_allocation_number?: string;
}

interface PDFTemplate {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
  };
  fonts: {
    hebrew: string;
    english: string;
  };
  layout: {
    margin: string;
    headerHeight: string;
    footerHeight: string;
  };
}

function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
}

function getTemplate(templateId: string): PDFTemplate {
  const templates: Record<string, PDFTemplate> = {
    modern: {
      name: 'Modern',
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        text: '#1e293b',
        background: '#ffffff'
      },
      fonts: {
        hebrew: 'Heebo, Arial, sans-serif',
        english: 'Inter, Arial, sans-serif'
      },
      layout: {
        margin: '1.5cm',
        headerHeight: '4cm',
        footerHeight: '2cm'
      }
    },
    classic: {
      name: 'Classic',
      colors: {
        primary: '#1f2937',
        secondary: '#6b7280',
        text: '#111827',
        background: '#ffffff'
      },
      fonts: {
        hebrew: 'Arial, sans-serif',
        english: 'Times, serif'
      },
      layout: {
        margin: '2cm',
        headerHeight: '3.5cm',
        footerHeight: '1.5cm'
      }
    },
    minimal: {
      name: 'Minimal',
      colors: {
        primary: '#000000',
        secondary: '#666666',
        text: '#333333',
        background: '#ffffff'
      },
      fonts: {
        hebrew: 'Heebo, Arial, sans-serif',
        english: 'Arial, sans-serif'
      },
      layout: {
        margin: '1cm',
        headerHeight: '3cm',
        footerHeight: '1cm'
      }
    }
  };

  return templates[templateId] || templates.modern;
}

function formatCurrency(amount: number, currency: string): string {
  if (currency === 'ILS') {
    return `₪${amount.toFixed(2)}`;
  }
  return `${amount.toFixed(2)} ${currency}`;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('he-IL');
}

function getDocumentTypeHebrew(documentType: string): string {
  const types: Record<string, string> = {
    'tax_invoice': 'חשבונית מס',
    'receipt': 'קבלה',
    'credit_note': 'זיכוי',
    'tax_invoice_receipt': 'חשבונית מס/קבלה'
  };
  return types[documentType] || documentType;
}

async function generateQRCode(documentUrl: string): Promise<string> {
  // TODO: Implement actual QR code generation
  // For now, return a placeholder SVG
  const qrSvg = `
    <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
      <rect width="100" height="100" fill="white" stroke="black"/>
      <text x="50" y="50" text-anchor="middle" font-size="8">QR Code</text>
    </svg>
  `;
  return `data:image/svg+xml;base64,${btoa(qrSvg)}`;
}

function generateHTML(document: DocumentData, template: PDFTemplate): string {
  const qrCodePlaceholder = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgICAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9IndoaXRlIiBzdHJva2U9ImJsYWNrIi8+CiAgICAgIDx0ZXh0IHg9IjUwIiB5PSI1MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSI4Ij5RUiBDb2RlPC90ZXh0PgogICAgPC9zdmc+";

  return `
<!DOCTYPE html>
<html dir="rtl" lang="he">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${getDocumentTypeHebrew(document.document_type)} ${document.document_number}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Heebo:wght@300;400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ${template.fonts.hebrew};
            color: ${template.colors.text};
            background: ${template.colors.background};
            line-height: 1.6;
            font-size: 14px;
        }
        
        .invoice-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: ${template.layout.margin};
            background: white;
            min-height: 297mm;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid ${template.colors.primary};
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-logo {
            width: 120px;
            height: auto;
            margin-bottom: 1rem;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: 700;
            color: ${template.colors.primary};
            margin-bottom: 0.5rem;
        }
        
        .company-details {
            color: ${template.colors.secondary};
            font-size: 12px;
        }
        
        .document-title {
            text-align: center;
            font-size: 28px;
            font-weight: 700;
            color: ${template.colors.primary};
            margin-bottom: 0.5rem;
        }
        
        .document-number {
            text-align: center;
            font-size: 16px;
            color: ${template.colors.secondary};
        }
        
        .parties {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            gap: 2rem;
        }
        
        .party {
            flex: 1;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .party-title {
            font-weight: 700;
            color: ${template.colors.primary};
            margin-bottom: 0.5rem;
            font-size: 16px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table th {
            background: ${template.colors.primary};
            color: white;
            font-weight: 600;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9fafb;
        }
        
        .totals {
            margin-top: 2rem;
            text-align: right;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .total-row.final {
            font-weight: 700;
            font-size: 18px;
            color: ${template.colors.primary};
            border-bottom: 3px solid ${template.colors.primary};
        }
        
        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .allocation {
            font-weight: 600;
            color: ${template.colors.primary};
        }
        
        .qr-code {
            width: 80px;
            height: 80px;
        }
        
        .notes {
            margin: 2rem 0;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
            border-right: 4px solid ${template.colors.primary};
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .invoice-container { margin: 0; padding: 1cm; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <header class="header">
            <div class="company-info">
                ${document.company.logo_url ? `<img src="${document.company.logo_url}" alt="לוגו חברה" class="company-logo">` : ''}
                <div class="company-name">${document.company.name_hebrew}</div>
                <div class="company-details">
                    <div>ח.פ: ${document.company.business_number}</div>
                    <div>מע"מ: ${document.company.vat_id}</div>
                    <div>${document.company.address_hebrew}</div>
                    <div>טל: ${document.company.phone}</div>
                    <div>אימייל: ${document.company.email}</div>
                </div>
            </div>
            <div>
                <div class="document-title">${getDocumentTypeHebrew(document.document_type)}</div>
                <div class="document-number">מספר: ${document.document_number}</div>
            </div>
        </header>

        <section class="parties">
            <div class="party">
                <div class="party-title">מאת:</div>
                <div>${document.company.name_hebrew}</div>
                <div>${document.company.address_hebrew}</div>
            </div>
            <div class="party">
                <div class="party-title">עבור:</div>
                <div>${document.customer.name_hebrew}</div>
                <div>${document.customer.billing_address_hebrew || ''}</div>
                ${document.customer.business_number ? `<div>ח.פ: ${document.customer.business_number}</div>` : ''}
                ${document.customer.vat_id ? `<div>מע"מ: ${document.customer.vat_id}</div>` : ''}
            </div>
        </section>

        <div style="display: flex; justify-content: space-between; margin: 1rem 0;">
            <div><strong>תאריך הנפקה:</strong> ${formatDate(document.issue_date)}</div>
            ${document.due_date ? `<div><strong>תאריך פירעון:</strong> ${formatDate(document.due_date)}</div>` : ''}
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>תיאור</th>
                    <th>כמות</th>
                    <th>מחיר יחידה</th>
                    <th>מע"מ</th>
                    <th>סה"כ</th>
                </tr>
            </thead>
            <tbody>
                ${document.items.map(item => `
                    <tr>
                        <td>${item.description_hebrew}</td>
                        <td>${item.quantity}</td>
                        <td>${formatCurrency(item.unit_price, document.currency)}</td>
                        <td>${item.vat_rate}%</td>
                        <td>${formatCurrency(item.line_total + item.vat_amount, document.currency)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <span>סה"כ לפני מע"מ:</span>
                <span>${formatCurrency(document.subtotal, document.currency)}</span>
            </div>
            <div class="total-row">
                <span>מע"מ:</span>
                <span>${formatCurrency(document.vat_amount, document.currency)}</span>
            </div>
            <div class="total-row final">
                <span>סה"כ לתשלום:</span>
                <span>${formatCurrency(document.total_amount, document.currency)}</span>
            </div>
        </div>

        ${document.notes ? `
            <div class="notes">
                <strong>הערות:</strong><br>
                ${document.notes}
            </div>
        ` : ''}

        <footer class="footer">
            <div>
                ${document.ita_allocation_number ? `<div class="allocation">מספר אישור: ${document.ita_allocation_number}</div>` : ''}
                <div style="font-size: 12px; color: ${template.colors.secondary}; margin-top: 1rem;">
                    מסמך זה נוצר באופן אוטומטי במערכת
                </div>
            </div>
            <img src="${qrCodePlaceholder}" alt="QR Code" class="qr-code">
        </footer>
    </div>
</body>
</html>`;
}

async function generateDocumentPDF(documentId: string, templateId: string = 'modern'): Promise<{ pdf_url: string; pdf_size: number }> {
  const supabase = createSupabaseClient();

  try {
    // 1. Fetch complete document data
    const { data: document, error: fetchError } = await supabase
      .from('documents')
      .select(`
        *,
        company:companies(*),
        customer:customers(*),
        items:document_items(*)
      `)
      .eq('id', documentId)
      .single();

    if (fetchError || !document) {
      throw new Error('Document not found');
    }

    // 2. Load template configuration
    const template = getTemplate(templateId);

    // 3. Generate HTML structure
    const html = generateHTML(document, template);

    // 4. Convert HTML to PDF (placeholder implementation)
    // In a real implementation, you would use Puppeteer or similar
    const pdfBuffer = new TextEncoder().encode(html); // Placeholder
    const pdfSize = pdfBuffer.length;

    // 5. Upload to Supabase Storage
    const fileName = `${document.company.id}/${new Date().getFullYear()}/${document.document_number}.pdf`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('documents')
      .upload(fileName, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: true
      });

    if (uploadError) {
      throw new Error(`Failed to upload PDF: ${uploadError.message}`);
    }

    // 6. Get public URL
    const { data: urlData } = supabase.storage
      .from('documents')
      .getPublicUrl(fileName);

    const pdfUrl = urlData.publicUrl;

    // 7. Update document with PDF URL
    await supabase
      .from('documents')
      .update({ pdf_url: pdfUrl })
      .eq('id', documentId);

    return {
      pdf_url: pdfUrl,
      pdf_size: pdfSize
    };
  } catch (error) {
    throw new Error(`PDF generation failed: ${error.message}`);
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const user = await authenticateUser(req);
    if (!user) {
      return errorResponse('Unauthorized', 401);
    }

    const { document_id, template_id } = await req.json();

    if (!document_id) {
      return errorResponse('Document ID is required', 400);
    }

    const result = await generateDocumentPDF(document_id, template_id);

    return successResponse(result, 'PDF generated successfully');
  } catch (error) {
    console.error('PDF generation error:', error);
    return errorResponse(error.message || 'Internal server error', 500);
  }
});
