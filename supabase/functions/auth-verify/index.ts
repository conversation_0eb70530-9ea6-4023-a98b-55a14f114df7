import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  handleCors,
} from '../_shared/utils.ts';

// HTML template for verification success
const successHTML = `
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>אימות אימייל הושלם בהצלחה</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 40px;
        }
        
        h1 {
            color: #1f2937;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        p {
            color: #6b7280;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .app-button {
            background: #667eea;
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.2s;
        }
        
        .app-button:hover {
            background: #5a67d8;
        }
        
        .footer {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>אימות אימייל הושלם בהצלחה!</h1>
        <p>
            החשבון שלך אומת בהצלחה. כעת תוכל להתחבר לאפליקציה עם האימייל והסיסמה שלך.
        </p>
        <a href="#" class="app-button" onclick="openApp()">
            חזור לאפליקציה
        </a>
        <div class="footer">
            מערכת חשבוניות ישראלית
        </div>
    </div>
    
    <script>
        function openApp() {
            // Try to open the mobile app with deep link
            window.location.href = 'fintechinvoice://auth/verified';
            
            // Fallback: show instructions after a delay
            setTimeout(() => {
                alert('אנא חזור לאפליקציה והתחבר עם האימייל והסיסמה שלך.');
            }, 1000);
        }
    </script>
</body>
</html>
`;

// HTML template for verification error
const errorHTML = (errorMessage: string) => `
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>שגיאה באימות אימייל</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f87171 0%, #dc2626 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }
        
        .error-icon {
            width: 80px;
            height: 80px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 40px;
        }
        
        h1 {
            color: #1f2937;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        p {
            color: #6b7280;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .error-details {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            color: #dc2626;
            font-size: 14px;
        }
        
        .retry-button {
            background: #667eea;
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.2s;
            margin-left: 12px;
        }
        
        .retry-button:hover {
            background: #5a67d8;
        }
        
        .app-button {
            background: #6b7280;
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.2s;
        }
        
        .app-button:hover {
            background: #4b5563;
        }
        
        .footer {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
            color: #9ca3af;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✗</div>
        <h1>שגיאה באימות אימייל</h1>
        <p>
            אירעה שגיאה בעת אימות כתובת האימייל שלך. אנא נסה שוב או פנה לתמיכה.
        </p>
        <div class="error-details">
            ${errorMessage}
        </div>
        <a href="#" class="retry-button" onclick="requestNewVerification()">
            שלח אימייל אימות חדש
        </a>
        <a href="#" class="app-button" onclick="openApp()">
            חזור לאפליקציה
        </a>
        <div class="footer">
            מערכת חשבוניות ישראלית
        </div>
    </div>
    
    <script>
        function openApp() {
            window.location.href = 'fintechinvoice://auth/error';
            setTimeout(() => {
                alert('אנא חזור לאפליקציה ונסה להירשם שוב או פנה לתמיכה.');
            }, 1000);
        }
        
        function requestNewVerification() {
            alert('אנא חזור לאפליקציה ובקש אימייל אימות חדש מהמסך הרלוונטי.');
            openApp();
        }
    </script>
</body>
</html>
`;

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    const url = new URL(req.url);
    const token = url.searchParams.get('token');
    const type = url.searchParams.get('type');

    if (!token || !type) {
      return new Response(errorHTML('חסרים פרמטרים נדרשים לאימות'), {
        status: 400,
        headers: { 'Content-Type': 'text/html; charset=utf-8' },
      });
    }

    if (type !== 'signup') {
      return new Response(errorHTML('סוג אימות לא נתמך'), {
        status: 400,
        headers: { 'Content-Type': 'text/html; charset=utf-8' },
      });
    }

    const supabase = createSupabaseClient();

    // Verify the email using the token
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'signup',
    });

    if (error) {
      console.error('Email verification error:', error);
      let errorMessage = 'שגיאה לא ידועה באימות האימייל';
      
      if (error.message.includes('expired')) {
        errorMessage = 'קישור האימות פג תוקף. אנא בקש אימייל אימות חדש.';
      } else if (error.message.includes('invalid')) {
        errorMessage = 'קישור האימות לא תקין. אנא בקש אימייל אימות חדש.';
      } else if (error.message.includes('already confirmed')) {
        errorMessage = 'האימייל כבר אומת. תוכל להתחבר לאפליקציה.';
      }
      
      return new Response(errorHTML(errorMessage), {
        status: 400,
        headers: { 'Content-Type': 'text/html; charset=utf-8' },
      });
    }

    if (data.user) {
      console.log('Email verified successfully for user:', data.user.email);
      return new Response(successHTML, {
        status: 200,
        headers: { 'Content-Type': 'text/html; charset=utf-8' },
      });
    } else {
      return new Response(errorHTML('לא ניתן לאמת את האימייל'), {
        status: 400,
        headers: { 'Content-Type': 'text/html; charset=utf-8' },
      });
    }
  } catch (error) {
    console.error('Verification endpoint error:', error);
    return new Response(errorHTML('שגיאת שרת פנימית'), {
      status: 500,
      headers: { 'Content-Type': 'text/html; charset=utf-8' },
    });
  }
});
