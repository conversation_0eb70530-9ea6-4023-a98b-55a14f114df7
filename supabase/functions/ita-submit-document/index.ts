import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders, errorResponse, successResponse } from '../_shared/utils.ts';
import { authenticateUser, logAudit } from '../_shared/utils.ts';

interface ITAPayload {
  MessageType: string;
  MessageVersion: string;
  UniqueID: string;
  Supplier: {
    VatID: string;
    Name: string;
    Address: string;
  };
  Customer: {
    VatID: string;
    Name: string;
    Address: string;
  };
  DocumentDetails: {
    DocumentType: string;
    DocumentNumber: string;
    IssueDate: string;
    Currency: string;
    ExchangeRate: number;
  };
  Items: Array<{
    Description: string;
    Quantity: number;
    UnitPrice: number;
    VATRate: number;
    LineTotal: number;
    VATAmount: number;
  }>;
  Totals: {
    TotalBeforeVAT: number;
    TotalVAT: number;
    TotalAmount: number;
  };
}

interface ITAResponse {
  success: boolean;
  allocation_number?: string;
  error?: string;
}

function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
}

function mapDocumentType(documentType: string): string {
  const typeMap: Record<string, string> = {
    'tax_invoice': 'חשבונית מס',
    'receipt': 'קבלה',
    'credit_note': 'זיכוי',
    'tax_invoice_receipt': 'חשבונית מס/קבלה',
  };
  return typeMap[documentType] || documentType;
}

async function buildITAPayload(document: any): Promise<ITAPayload> {
  return {
    MessageType: "Invoice",
    MessageVersion: "1.0",
    UniqueID: document.id,
    Supplier: {
      VatID: document.company.vat_id,
      Name: document.company.name,
      Address: document.company.address_hebrew
    },
    Customer: {
      VatID: document.customer.vat_id,
      Name: document.customer.name,
      Address: document.customer.billing_address_hebrew
    },
    DocumentDetails: {
      DocumentType: mapDocumentType(document.document_type),
      DocumentNumber: document.document_number,
      IssueDate: document.issue_date,
      Currency: document.currency,
      ExchangeRate: document.currency === 'ILS' ? 1.0 : 1.0 // TODO: Implement exchange rate lookup
    },
    Items: document.items.map((item: any) => ({
      Description: item.description_hebrew,
      Quantity: item.quantity,
      UnitPrice: item.unit_price,
      VATRate: item.vat_rate,
      LineTotal: item.line_total,
      VATAmount: item.vat_amount
    })),
    Totals: {
      TotalBeforeVAT: document.subtotal,
      TotalVAT: document.vat_amount,
      TotalAmount: document.total_amount
    }
  };
}

async function signPayload(payload: ITAPayload): Promise<string> {
  // TODO: Implement actual digital signature with PKCS#7
  // For now, return base64 encoded JSON as placeholder
  const jsonString = JSON.stringify(payload);
  const encoder = new TextEncoder();
  const data = encoder.encode(jsonString);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return btoa(hashHex);
}

async function submitToITAAPI(payload: ITAPayload, signature: string): Promise<ITAResponse> {
  const itaApiUrl = Deno.env.get('ITA_API_URL') || 'https://api.taxes.gov.il/shaam/v1';
  const itaApiKey = Deno.env.get('ITA_API_KEY');

  if (!itaApiKey) {
    return {
      success: false,
      error: 'ITA API key not configured'
    };
  }

  try {
    const response = await fetch(`${itaApiUrl}/documents`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${itaApiKey}`,
        'Content-Type': 'application/json',
        'X-Digital-Signature': signature,
      },
      body: JSON.stringify(payload)
    });

    if (response.ok) {
      const result = await response.json();
      return {
        success: true,
        allocation_number: result.allocation_number
      };
    } else {
      const errorData = await response.text();
      return {
        success: false,
        error: `ITA API error: ${response.status} - ${errorData}`
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Network error: ${error.message}`
    };
  }
}

async function submitDocumentToITA(documentId: string): Promise<ITAResponse> {
  const supabase = createSupabaseClient();

  try {
    // 1. Fetch document with all relations
    const { data: document, error: fetchError } = await supabase
      .from('documents')
      .select(`
        *,
        company:companies(*),
        customer:customers(*),
        items:document_items(*)
      `)
      .eq('id', documentId)
      .single();

    if (fetchError || !document) {
      return {
        success: false,
        error: 'Document not found'
      };
    }

    // 2. Validate document readiness
    // TODO: For testing, accept both 'draft' and 'pending_allocation' status
    if (document.status !== 'pending_allocation' && document.status !== 'draft') {
      return {
        success: false,
        error: 'Document status must be pending_allocation or draft'
      };
    }

    if (!document.customer.vat_id) {
      return {
        success: false,
        error: 'Customer must have valid VAT ID'
      };
    }

    // 3. Build ITA JSON payload
    const payload = await buildITAPayload(document);

    // 4. Sign payload
    const signature = await signPayload(payload);

    // 5. Submit to ITA API
    const result = await submitToITAAPI(payload, signature);

    // 6. Handle response
    if (result.success) {
      // Update document status and allocation number
      await supabase
        .from('documents')
        .update({
          status: 'sent',
          ita_allocation_number: result.allocation_number,
          ita_submitted_at: new Date().toISOString(),
          sent_at: new Date().toISOString(),
          sent_via: 'ita_system'
        })
        .eq('id', documentId);

      // Update ITA queue status
      await supabase
        .from('ita_queue')
        .update({
          status: 'success',
          response_payload: result,
          completed_at: new Date().toISOString()
        })
        .eq('document_id', documentId);
    } else {
      // Update ITA queue with error
      await supabase
        .from('ita_queue')
        .update({
          status: 'failed',
          error_message: result.error,
          last_attempt_at: new Date().toISOString()
        })
        .eq('document_id', documentId);
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: `Unexpected error: ${error.message}`
    };
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const user = await authenticateUser(req);
    if (!user) {
      return errorResponse('Unauthorized', 401);
    }

    const { document_id } = await req.json();

    if (!document_id) {
      return errorResponse('Document ID is required', 400);
    }

    const result = await submitDocumentToITA(document_id);

    if (result.success) {
      return successResponse(result, 'Document submitted to ITA successfully');
    } else {
      return errorResponse(result.error || 'ITA submission failed', 400);
    }
  } catch (error) {
    console.error('ITA submission error:', error);
    return errorResponse('Internal server error', 500);
  }
});
