import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  validateEmail,
  logAudit,
  checkRateLimit,
} from '../_shared/utils.ts';
import { LoginRequest, ValidationError } from '../_shared/types.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Rate limiting
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(`login:${clientIP}`, 10, 15 * 60 * 1000)) {
      return errorResponse('Too many login attempts. Please try again later.', 429);
    }

    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    const body: LoginRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.email || !validateEmail(body.email)) {
      validationErrors.push({ field: 'email', message: 'Valid email is required' });
    }

    if (!body.password || body.password.length < 1) {
      validationErrors.push({ field: 'password', message: 'Password is required' });
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Authenticate user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: body.email,
      password: body.password,
    });

    if (authError) {
      console.error('Auth error:', authError);
      return errorResponse('Invalid email or password', 401);
    }

    const userId = authData.user.id;

    // Get user details
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      console.error('User fetch error:', userError);
      return errorResponse('User not found', 404);
    }

    // Get user's companies
    const { data: companiesData, error: companiesError } = await supabase
      .from('company_users')
      .select(`
        role,
        created_at,
        company:companies(*)
      `)
      .eq('user_id', userId);

    if (companiesError) {
      console.error('Companies fetch error:', companiesError);
      return errorResponse('Failed to fetch user companies', 500);
    }

    // Update last login
    await supabase
      .from('users')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', userId);

    // Log audit for each company
    for (const companyUser of companiesData) {
      await logAudit(
        companyUser.company.id,
        userId,
        'login',
        'user_session',
        userId,
        null,
        { login_time: new Date().toISOString() },
        req
      );
    }

    return successResponse({
      user: userData,
      companies: companiesData.map(cu => ({
        ...cu.company,
        user_role: cu.role,
        joined_at: cu.created_at,
      })),
      session: {
        access_token: authData.session.access_token,
        refresh_token: authData.session.refresh_token,
        expires_at: authData.session.expires_at,
        token_type: authData.session.token_type,
      },
    }, 'Login successful');

  } catch (error) {
    console.error('Unexpected error:', error);
    return errorResponse('Internal server error', 500);
  }
});
