import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  validationErrorResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  validateEmail,
  validateIsraeliPhone,
  logAudit,
} from '../_shared/utils.ts';
import { ValidationError } from '../_shared/types.ts';

interface SendDocumentRequest {
  method: 'email' | 'whatsapp';
  recipient_email?: string;
  recipient_phone?: string;
}

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'POST') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Extract document ID from URL
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const documentId = pathParts[pathParts.length - 2]; // Assuming URL like /documents/{id}/send

    if (!documentId) {
      return errorResponse('Document ID is required', 400);
    }

    const body: SendDocumentRequest = await req.json();
    
    // Validate input
    const validationErrors: ValidationError[] = [];

    if (!body.method) {
      validationErrors.push({ field: 'method', message: 'Sending method is required' });
    } else if (!['email', 'whatsapp'].includes(body.method)) {
      validationErrors.push({ field: 'method', message: 'Method must be email or whatsapp' });
    }

    if (body.method === 'email') {
      if (!body.recipient_email || !validateEmail(body.recipient_email)) {
        validationErrors.push({ field: 'recipient_email', message: 'Valid recipient email is required for email method' });
      }
    }

    if (body.method === 'whatsapp') {
      if (!body.recipient_phone || !validateIsraeliPhone(body.recipient_phone)) {
        validationErrors.push({ field: 'recipient_phone', message: 'Valid Israeli phone number is required for WhatsApp method' });
      }
    }

    if (validationErrors.length > 0) {
      return validationErrorResponse(validationErrors);
    }

    const supabase = createSupabaseClient();

    // Get document with all related data
    const { data: document, error: documentError } = await supabase
      .from('documents')
      .select(`
        *,
        company:companies(*),
        customer:customers(*),
        items:document_items(*)
      `)
      .eq('id', documentId)
      .single();

    if (documentError || !document) {
      return errorResponse('Document not found', 404);
    }

    // Check user access to company
    await checkUserRole(user.id, document.company_id, ['admin', 'user', 'accountant']);

    // Check if document is in a sendable state
    if (!['draft', 'pending_allocation', 'sent'].includes(document.status)) {
      return errorResponse('Document must be in draft, pending_allocation, or sent status to be sent', 400);
    }

    // If document is draft, transition to pending_allocation and queue for ITA
    if (document.status === 'draft') {
      // Update status to pending_allocation
      await supabase
        .from('documents')
        .update({ status: 'pending_allocation' })
        .eq('id', documentId);

      // Queue for ITA submission
      await supabase
        .from('ita_queue')
        .insert({
          document_id: documentId,
          status: 'pending',
          request_payload: {
            document_id: documentId,
            document_type: document.document_type,
            document_number: document.document_number,
          },
          next_retry_at: new Date().toISOString(),
        });

      // Update our local document object
      document.status = 'pending_allocation';
    }

    // Ensure PDF exists
    if (!document.pdf_url) {
      // Generate PDF if it doesn't exist
      const { data: pdfData, error: pdfError } = await supabase.functions.invoke('generate-pdf', {
        body: { document_id: documentId }
      });

      if (pdfError) {
        console.error('PDF generation error:', pdfError);
        return errorResponse('Failed to generate PDF', 500);
      }

      // Update document with PDF URL
      await supabase
        .from('documents')
        .update({ pdf_url: pdfData.pdf_url })
        .eq('id', documentId);

      document.pdf_url = pdfData.pdf_url;
    }

    let deliveryResult;

    if (body.method === 'email') {
      // Send via email
      deliveryResult = await sendDocumentByEmail(document, body.recipient_email!);
    } else {
      // Send via WhatsApp
      deliveryResult = await sendDocumentByWhatsApp(document, body.recipient_phone!);
    }

    if (!deliveryResult.success) {
      return errorResponse(deliveryResult.error || 'Failed to send document', 500);
    }

    // Update document status
    const now = new Date().toISOString();
    const { error: updateError } = await supabase
      .from('documents')
      .update({
        status: 'sent',
        sent_at: now,
        sent_via: body.method,
      })
      .eq('id', documentId);

    if (updateError) {
      console.error('Document update error:', updateError);
    }

    // Log audit
    await logAudit(
      document.company_id,
      user.id,
      'send',
      'document',
      documentId,
      { status: document.status },
      { 
        status: 'sent', 
        sent_via: body.method,
        recipient: body.method === 'email' ? body.recipient_email : body.recipient_phone
      },
      req
    );

    return successResponse({
      status: 'sent',
      sent_at: now,
      delivery_method: body.method,
      delivery_details: deliveryResult.details,
    }, 'Document sent successfully');

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});

async function sendDocumentByEmail(document: any, recipientEmail: string) {
  try {
    // Build email content
    const subject = `${getDocumentTypeHebrew(document.document_type)} מספר ${document.document_number}`;
    const body = `
שלום,

מצורף ${getDocumentTypeHebrew(document.document_type)} מספר ${document.document_number}
מתאריך ${formatDate(document.issue_date)}
על סך ${document.total_amount} ${document.currency}

לצפייה והורדה: ${document.pdf_url}

תודה,
${document.company.name_hebrew}
    `.trim();

    // Here you would integrate with your email service (SendGrid, AWS SES, etc.)
    // For now, we'll simulate the email sending
    console.log('Sending email to:', recipientEmail);
    console.log('Subject:', subject);
    console.log('Body:', body);

    return {
      success: true,
      details: {
        recipient: recipientEmail,
        subject: subject,
        sent_at: new Date().toISOString(),
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to send email'
    };
  }
}

async function sendDocumentByWhatsApp(document: any, recipientPhone: string) {
  try {
    // Build WhatsApp message
    const message = `
שלום,

מצורף ${getDocumentTypeHebrew(document.document_type)} מספר ${document.document_number}
מתאריך ${formatDate(document.issue_date)}
על סך ${document.total_amount} ${document.currency}

לצפייה והורדה:
${document.pdf_url}

תודה,
${document.company.name_hebrew}
    `.trim();

    // Generate WhatsApp URL
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${recipientPhone.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;

    return {
      success: true,
      details: {
        recipient: recipientPhone,
        whatsapp_url: whatsappUrl,
        message: message,
        sent_at: new Date().toISOString(),
      }
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to generate WhatsApp message'
    };
  }
}

function getDocumentTypeHebrew(type: string): string {
  const types: Record<string, string> = {
    'tax_invoice': 'חשבונית מס',
    'receipt': 'קבלה',
    'credit_note': 'זיכוי',
    'tax_invoice_receipt': 'חשבונית מס/קבלה',
  };
  return types[type] || type;
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('he-IL');
}
