import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import {
  createSupabaseClient,
  corsHeaders,
  errorResponse,
  successResponse,
  handleCors,
  getUserFromToken,
  checkUserRole,
  logAudit,
} from '../_shared/utils.ts';

serve(async (req) => {
  // Handle CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    if (req.method !== 'GET') {
      return errorResponse('Method not allowed', 405);
    }

    // Get user from token
    const user = await getUserFromToken(req);
    
    // Get query parameters
    const url = new URL(req.url);
    const companyId = url.searchParams.get('company_id');
    const documentType = url.searchParams.get('document_type');

    if (!companyId) {
      return errorResponse('company_id is required', 400);
    }

    if (!documentType) {
      return errorResponse('document_type is required', 400);
    }

    // Validate document type
    const validTypes = ['tax_invoice', 'receipt', 'credit_note', 'tax_invoice_receipt'];
    if (!validTypes.includes(documentType)) {
      return errorResponse('Invalid document_type', 400);
    }

    // Check user access to company
    await checkUserRole(user.id, companyId, ['admin', 'user', 'accountant']);

    const supabase = createSupabaseClient();

    // Get or create document sequence
    let { data: sequence, error: sequenceError } = await supabase
      .from('document_sequences')
      .select('*')
      .eq('company_id', companyId)
      .eq('document_type', documentType)
      .single();

    if (sequenceError && sequenceError.code !== 'PGRST116') {
      console.error('Sequence fetch error:', sequenceError);
      return errorResponse('Failed to fetch document sequence', 500);
    }

    // If sequence doesn't exist, create it
    if (!sequence) {
      const currentYear = new Date().getFullYear();
      const prefix = `${documentType.toUpperCase().replace('_', '-')}-${currentYear}-`;
      
      const { data: newSequence, error: createError } = await supabase
        .from('document_sequences')
        .insert({
          company_id: companyId,
          document_type: documentType,
          prefix: prefix,
          current_number: 0,
        })
        .select()
        .single();

      if (createError) {
        console.error('Sequence creation error:', createError);
        return errorResponse('Failed to create document sequence', 500);
      }

      sequence = newSequence;
    }

    // Calculate next number
    const nextNumber = sequence.current_number + 1;
    const paddedNumber = nextNumber.toString().padStart(5, '0');
    const fullNumber = `${sequence.prefix}${paddedNumber}`;

    // Log audit
    await logAudit(
      companyId,
      user.id,
      'view',
      'document_sequence',
      sequence.id,
      null,
      { next_number: fullNumber },
      req
    );

    return successResponse({
      next_number: fullNumber,
      prefix: sequence.prefix,
      sequence: nextNumber,
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    if (error.message.includes('No access') || error.message.includes('Insufficient')) {
      return errorResponse(error.message, 403);
    }
    if (error.message.includes('Invalid token')) {
      return errorResponse('Unauthorized', 401);
    }
    return errorResponse('Internal server error', 500);
  }
});
