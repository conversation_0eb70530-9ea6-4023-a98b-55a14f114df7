{"version": "5", "redirects": {"https://esm.sh/@supabase/node-fetch@^2.6.13?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/storage-js@^2.10.4?target=denonext": "https://esm.sh/@supabase/storage-js@2.11.0?target=denonext", "https://esm.sh/@supabase/supabase-js@2": "https://esm.sh/@supabase/supabase-js@2.56.1", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext"}, "remote": {"https://deno.land/std@0.168.0/fmt/colors.ts": "03ad95e543d2808bc43c17a3dd29d25b43d0f16287fe562a0be89bf632454a12", "https://deno.land/std@0.168.0/testing/_diff.ts": "a23e7fc2b4d8daa3e158fa06856bedf5334ce2a2831e8bf9e509717f455adb2c", "https://deno.land/std@0.168.0/testing/_format.ts": "cd11136e1797791045e639e9f0f4640d5b4166148796cad37e6ef75f7d7f3832", "https://deno.land/std@0.168.0/testing/asserts.ts": "51353e79437361d4b02d8e32f3fc83b22231bc8f8d4c841d86fd32b0b0afe940", "https://esm.sh/@supabase/auth-js@2.71.1/denonext/auth-js.mjs": "d55f67342e652b8bdce35b0ff13ad5cc294b7e96dbd68f859b464b07c6864967", "https://esm.sh/@supabase/functions-js@2.4.5/denonext/functions-js.mjs": "acfcb4db90c292c335a9dbb5664485f5e0b680533d68e6942abb9387ec7e0d0b", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.21.3/denonext/postgrest-js.mjs": "a9567fcd2c62b498d01514aebf82beb11acdd8242833d081bdc61d1f0227f9ef", "https://esm.sh/@supabase/realtime-js@2.15.4/denonext/realtime-js.mjs": "014803e3c1776dfe9c0c05c4f890252c66afce791fe1d93ecf687d7e773febcc", "https://esm.sh/@supabase/storage-js@2.11.0/denonext/storage-js.mjs": "86f098636aef56302e6a7389b8eba859874180b0857661be196c3cce387b6461", "https://esm.sh/@supabase/storage-js@2.11.0?target=denonext": "d7e8b6651554f79445c88d1beaa8c5e2fcb960a6f67449e4903f532ea3301539", "https://esm.sh/@supabase/supabase-js@2.56.1": "5cc7adee6789b3c1bd9959694ee1564da6005b87ef7bae8fd4e595e5959a5ccd", "https://esm.sh/@supabase/supabase-js@2.56.1/denonext/supabase-js.mjs": "5b73fda4c976f0061ce33ba4f403bbb03a254304d93a4eb610d1a27d10404675", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d"}, "workspace": {"packageJson": {"dependencies": ["npm:@types/node@^22.5.5", "npm:typescript@^5.5.3"]}, "members": {"apps/landing": {"packageJson": {"dependencies": ["npm:@eslint/js@^9.9.0", "npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-slot@^1.2.3", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.4", "npm:@tailwindcss/typography@~0.5.15", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:globals@^15.9.0", "npm:lovable-tagger@^1.1.7", "npm:lucide-react@0.462", "npm:next-themes@0.3", "npm:postcss@^8.4.47", "npm:react-dom@^18.3.1", "npm:react@^18.3.1", "npm:sonner@^1.5.0", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.5.3", "npm:vite@^5.4.1"]}}, "apps/web": {"packageJson": {"dependencies": ["npm:@eslint/js@^9.9.0", "npm:@supabase/supabase-js@^2.45.4", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.56.2", "npm:@tanstack/react-router-devtools@^1.58.3", "npm:@tanstack/react-router@^1.58.3", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:globals@^15.9.0", "npm:postcss@^8.4.47", "npm:react-dom@^18.3.1", "npm:react-hook-form@^7.53.0", "npm:react@^18.3.1", "npm:sonner@^1.5.0", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.5.3", "npm:vite@^5.4.1", "npm:vitest@^2.0.5", "npm:zustand@^4.5.5"]}}, "packages/types": {"packageJson": {"dependencies": ["npm:typescript@^5.5.3"]}}, "packages/ui": {"packageJson": {"dependencies": ["npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-slot@^1.2.3", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.4", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:lucide-react@0.462", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:typescript@^5.5.3"]}}, "packages/utils": {"packageJson": {"dependencies": ["npm:typescript@^5.5.3"]}}}}}